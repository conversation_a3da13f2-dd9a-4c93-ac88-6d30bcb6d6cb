[gd_scene load_steps=31 format=3 uid="uid://nhp56i6xjbdx"]

[ext_resource type="Script" uid="uid://4svwvf0dcnp2" path="res://script/main_menu.gd" id="1_main"]
[ext_resource type="Shader" uid="uid://b3lc25sp1c3e0" path="res://shader/main_menu.gdshader" id="2_3bgmf"]
[ext_resource type="Texture2D" uid="uid://s861v2v4bdtn" path="res://assert/back/menu_back_1.png" id="2_17tuy"]
[ext_resource type="Shader" uid="uid://b481i240hxr7a" path="res://shader/main_menu_fog.gdshader" id="3_3buhr"]
[ext_resource type="PackedScene" uid="uid://bxnv6cucb8e66" path="res://scence/settings_panel.tscn" id="4_17vd0"]
[ext_resource type="Texture2D" uid="uid://tdmbrgqbbd8g" path="res://assert/main_menu/panel.png" id="7_xknjj"]
[ext_resource type="Texture2D" uid="uid://cjh6r3tys0ql1" path="res://assert/words/upcards2.png" id="8_xknjj"]
[ext_resource type="PackedScene" uid="uid://b2sj4qb5ot54p" path="res://scence/collision_spark_particles.tscn" id="9_fjlrm"]
[ext_resource type="PackedScene" uid="uid://csgr12uwl10ik" path="res://scence/firefly_particles.tscn" id="10_xpdu6"]

[sub_resource type="Animation" id="Animation_xknjj"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:energy")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [1.0]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("../UpLight1:energy")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [1.0]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("../upBackFlash:energy")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [1.0]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("../cardsBackFlash:energy")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [1.0]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("../BackFlash:energy")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [1.0]
}

[sub_resource type="Animation" id="Animation_17tuy"]
resource_name = "flash"
length = 5.0
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:energy")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0.3, 0.36, 0.42, 0.5, 0.6, 0.7),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1),
"update": 1,
"values": [1.0, 0.5, 0.0, 1.0, 0.0, 1.0]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("../UpLight1:energy")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.06, 0.12, 0.2, 0.3, 0.4),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1),
"update": 1,
"values": [1.0, 0.5, 0.0, 1.0, 0.0, 1.0]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("../upBackFlash:energy")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 0.06, 0.12, 0.200008, 0.3, 0.395366),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1),
"update": 1,
"values": [1.0, 0.5, 0.0, 1.0, 0.0, 1.0]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("../cardsBackFlash:energy")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0.3, 0.36, 0.42, 0.5, 0.6, 0.70003),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1),
"update": 1,
"values": [0.8, 0.3, 0.0, 0.8, 0.0, 0.8]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("../BackFlash:energy")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0, 0.06, 0.12, 0.2, 0.3, 0.36, 0.42, 0.5, 0.6, 0.7),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1, 1, 1),
"update": 1,
"values": [1.0, 0.5, 0.0, 1.0, 1.0, 0.3, 1.0, 1.0, 0.0, 1.0]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_syaut"]
_data = {
&"RESET": SubResource("Animation_xknjj"),
&"flash": SubResource("Animation_17tuy")
}

[sub_resource type="OccluderPolygon2D" id="OccluderPolygon2D_17tuy"]
closed = false
polygon = PackedVector2Array(-1.52588e-05, 560, 53, 560, 98, 560, 137, 558, 184, 555, 225, 551, 258, 546, 290, 539, 310, 534, 323, 525, 320, 514, 309, 509, 296, 506, 288, 502, 286, 492, 278, 489, 268, 489, 262, 491, 256, 496, 247, 498, 225, 496, 204, 495, 179, 495, 162, 494, 141, 460, 134, 449, 116, 442, 95, 444, 80, 458, 74, 479, 71, 489, 60, 488, 49, 489, 42, 487, 36, 479, 41, 467, 35, 457, 28, 448, 11, 449, 2.99998, 457, -1.52588e-05, 457)

[sub_resource type="Gradient" id="Gradient_quvbe"]
interpolation_mode = 2
colors = PackedColorArray(1, 1, 1, 1, 0, 0, 0, 1)

[sub_resource type="GradientTexture2D" id="GradientTexture2D_j3kgy"]
gradient = SubResource("Gradient_quvbe")
fill = 1
fill_from = Vector2(0.477064, 0.522936)
fill_to = Vector2(1, 0.517391)

[sub_resource type="Animation" id="Animation_quvbe"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("LightAnimationPlayer/BallLight2D2:energy")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("LightAnimationPlayer/BallLight2D:energy")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [1.0]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("LightAnimationPlayer/UpLight2D:energy")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.5]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("LightAnimationPlayer/PointLight1:energy")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.5]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("LightAnimationPlayer/PointLight2:energy")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.3]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("LightAnimationPlayer/PointLight3:energy")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("LightAnimationPlayer/WinLight4:energy")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("LightAnimationPlayer/WinLight5:energy")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("LightAnimationPlayer/PointLight5:energy")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("LightAnimationPlayer/PointLight4:energy")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("LightAnimationPlayer/MagicLight:energy")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/11/type = "value"
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/path = NodePath("LightAnimationPlayer/FlowLight1:position")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1062, 173)]
}
tracks/12/type = "value"
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/path = NodePath("LightAnimationPlayer/FlowLight2:position")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1144, 168)]
}

[sub_resource type="Animation" id="Animation_syaut"]
resource_name = "light"
length = 4.0
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("LightAnimationPlayer/BallLight2D:energy")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 2, 4),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 1.5, 0.0]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("LightAnimationPlayer/BallLight2D2:energy")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(2, 3, 4),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.5, 0.0]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("LightAnimationPlayer/UpLight2D:energy")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 2, 4),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.5, 0.0, 0.5]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("LightAnimationPlayer/PointLight1:energy")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0, 1, 2, 3, 4),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1),
"update": 0,
"values": [0.5, 0.0, 0.5, 0.0, 0.5]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("LightAnimationPlayer/PointLight2:energy")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0.5, 1.5, 3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.4, 0.0, 0.3]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("LightAnimationPlayer/PointLight3:energy")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0, 1, 2, 3, 4),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1),
"update": 0,
"values": [0.0, 0.5, 0.0, 0.5, 0.0]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("LightAnimationPlayer/WinLight4:energy")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0, 0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1, 1),
"update": 0,
"values": [0.0, 0.5, 0.0, 0.8, 0.5, 0.8, 0.0, 0.5, 0.0]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("LightAnimationPlayer/WinLight5:energy")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0, 0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1, 1),
"update": 0,
"values": [0.0, 0.5, 0.0, 0.8, 0.5, 0.8, 0.0, 0.5, 0.0]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("LightAnimationPlayer/PointLight5:energy")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(0, 1, 2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.8, 0.0]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("LightAnimationPlayer/PointLight4:energy")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(0, 1, 2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.8, 0.0]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("LightAnimationPlayer/MagicLight:energy")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0, 2, 4),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 1.0, 0.0]
}
tracks/11/type = "value"
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/path = NodePath("LightAnimationPlayer/FlowLight1:position")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = {
"times": PackedFloat32Array(0, 1, 2, 3, 4),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(1062, 173), Vector2(1059, 140), Vector2(1093, 101), Vector2(1124, 82), Vector2(1158, 82)]
}
tracks/12/type = "value"
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/path = NodePath("LightAnimationPlayer/FlowLight2:position")
tracks/12/interp = 2
tracks/12/loop_wrap = true
tracks/12/keys = {
"times": PackedFloat32Array(0, 1, 2, 3, 4),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(1191, 156), Vector2(1144, 168), Vector2(1094, 202), Vector2(1158, 264), Vector2(1196, 263)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_j3kgy"]
_data = {
&"RESET": SubResource("Animation_quvbe"),
&"light": SubResource("Animation_syaut")
}

[sub_resource type="Gradient" id="Gradient_17tuy"]
interpolation_mode = 2
offsets = PackedFloat32Array(0.00649351, 1)
colors = PackedColorArray(1, 1, 1, 1, 0, 0, 0, 1)

[sub_resource type="GradientTexture2D" id="GradientTexture2D_xknjj"]
gradient = SubResource("Gradient_17tuy")
fill = 1
fill_from = Vector2(0.5, 0.5)
fill_to = Vector2(1, 0.518349)

[sub_resource type="Gradient" id="Gradient_xknjj"]
interpolation_mode = 2
colors = PackedColorArray(1, 1, 1, 1, 0, 0, 0, 1)

[sub_resource type="GradientTexture2D" id="GradientTexture2D_syaut"]
gradient = SubResource("Gradient_xknjj")
fill = 1
fill_from = Vector2(0.5, 0.5)
fill_to = Vector2(1, 0.513761)

[sub_resource type="ShaderMaterial" id="ShaderMaterial_17vd0"]
shader = ExtResource("2_3bgmf")
shader_parameter/base_col = Color(0.25849, 0.0007879, 0.564934, 1)
shader_parameter/border_thickness = 0.02
shader_parameter/isoline_offset = 0.4
shader_parameter/isoline_offset2 = 0.325
shader_parameter/pattern_scale = 12.0
shader_parameter/animation_speed = 0.25
shader_parameter/time_multiplier = 1.0

[sub_resource type="ShaderMaterial" id="ShaderMaterial_3buhr"]
shader = ExtResource("3_3buhr")
shader_parameter/resolution = Vector2(320, 100)
shader_parameter/scan_line_amount = 0.5
shader_parameter/warp_amount = 0.0
shader_parameter/vignette_amount = 0.5
shader_parameter/vignette_intensity = 0.3
shader_parameter/grille_amount = 0.05
shader_parameter/brightness_boost = 1.2

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_68j3p"]
draw_center = false
corner_radius_top_left = 20
corner_radius_top_right = 20
corner_radius_bottom_right = 20
corner_radius_bottom_left = 20

[sub_resource type="Gradient" id="Gradient_syaut"]
colors = PackedColorArray(1, 1, 1, 1, 0, 0, 0, 1)

[sub_resource type="GradientTexture2D" id="GradientTexture2D_quvbe"]
gradient = SubResource("Gradient_syaut")
fill = 1
fill_from = Vector2(0.504587, 0.522936)
fill_to = Vector2(1, 0.53211)

[sub_resource type="Animation" id="Animation_lhn3w"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:energy")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [1.0]
}

[sub_resource type="Animation" id="Animation_j3kgy"]
resource_name = "words_light"
length = 4.0
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath(".:energy")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3, 0.4),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1),
"update": 1,
"values": [0.5, 0.0, 0.6, 0.0, 0.5]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_4k4jm"]
_data = {
&"RESET": SubResource("Animation_lhn3w"),
&"words_light": SubResource("Animation_j3kgy")
}

[node name="MainMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_main")

[node name="back_pic" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("2_17tuy")
expand_mode = 1
stretch_mode = 4

[node name="Light" type="Control" parent="."]
anchors_preset = 0
offset_right = 40.0
offset_bottom = 40.0

[node name="FlashAnimationPlayer" type="AnimationPlayer" parent="Light"]
root_node = NodePath("CardsLight")
libraries = {
&"": SubResource("AnimationLibrary_syaut")
}
autoplay = "flash"

[node name="LightOccluder2D" type="LightOccluder2D" parent="Light/FlashAnimationPlayer"]
position = Vector2(1.52588e-05, 0)
occluder = SubResource("OccluderPolygon2D_17tuy")

[node name="CardsLight" type="PointLight2D" parent="Light/FlashAnimationPlayer"]
position = Vector2(272.5, 242.5)
scale = Vector2(8.39063, 4.04688)
color = Color(1, 1, 1, 0.541176)
shadow_filter = 1
texture = SubResource("GradientTexture2D_j3kgy")

[node name="cardsBackFlash" type="PointLight2D" parent="Light/FlashAnimationPlayer"]
position = Vector2(262, 520.5)
scale = Vector2(9.40625, 1.92188)
color = Color(1, 1, 1, 0.121569)
shadow_enabled = true
shadow_filter = 1
shadow_filter_smooth = 25.0
texture = SubResource("GradientTexture2D_j3kgy")

[node name="UpLight1" type="PointLight2D" parent="Light/FlashAnimationPlayer"]
position = Vector2(155.5, 136.5)
scale = Vector2(4.10937, 2.95312)
color = Color(1, 1, 1, 0.541176)
shadow_filter = 1
texture = SubResource("GradientTexture2D_j3kgy")

[node name="upBackFlash" type="PointLight2D" parent="Light/FlashAnimationPlayer"]
position = Vector2(153.5, 538)
scale = Vector2(4.70313, 1.40625)
color = Color(1, 1, 1, 0.0784314)
shadow_enabled = true
shadow_filter = 1
shadow_filter_smooth = 25.0
texture = SubResource("GradientTexture2D_j3kgy")

[node name="BackFlash" type="PointLight2D" parent="Light/FlashAnimationPlayer"]
position = Vector2(301.5, 589)
scale = Vector2(2.23437, 0.5625)
color = Color(1, 1, 1, 0.0784314)
shadow_enabled = true
shadow_filter = 1
shadow_filter_smooth = 25.0
texture = SubResource("GradientTexture2D_j3kgy")

[node name="LightAnimationPlayer" type="AnimationPlayer" parent="Light"]
libraries = {
&"": SubResource("AnimationLibrary_j3kgy")
}
autoplay = "light"

[node name="MagicLight" type="PointLight2D" parent="Light/LightAnimationPlayer"]
position = Vector2(508.5, 281.5)
scale = Vector2(1.10937, 2.10938)
energy = 0.0
texture = SubResource("GradientTexture2D_xknjj")

[node name="BallLight2D" type="PointLight2D" parent="Light/LightAnimationPlayer"]
position = Vector2(110, 474.5)
scale = Vector2(2.84375, 2.39063)
texture = SubResource("GradientTexture2D_xknjj")

[node name="UpLight2D" type="PointLight2D" parent="Light/LightAnimationPlayer"]
position = Vector2(464, 108)
scale = Vector2(4.59375, 3.71875)
energy = 0.5
texture = SubResource("GradientTexture2D_xknjj")

[node name="BallLight2D2" type="PointLight2D" parent="Light/LightAnimationPlayer"]
position = Vector2(462, 421)
scale = Vector2(1.28125, 0.90625)
energy = 0.0
texture = SubResource("GradientTexture2D_xknjj")

[node name="PointLight1" type="PointLight2D" parent="Light/LightAnimationPlayer"]
position = Vector2(556.5, 181)
scale = Vector2(0.453125, 0.4375)
energy = 0.5
texture = SubResource("GradientTexture2D_xknjj")

[node name="PointLight2" type="PointLight2D" parent="Light/LightAnimationPlayer"]
position = Vector2(640, 169)
scale = Vector2(0.453125, 0.4375)
energy = 0.3
texture = SubResource("GradientTexture2D_xknjj")

[node name="PointLight3" type="PointLight2D" parent="Light/LightAnimationPlayer"]
position = Vector2(520, 189)
scale = Vector2(0.453125, 0.4375)
energy = 0.0
texture = SubResource("GradientTexture2D_xknjj")

[node name="PointLight4" type="PointLight2D" parent="Light/LightAnimationPlayer"]
position = Vector2(1016, 41)
scale = Vector2(0.453125, 0.4375)
energy = 0.0
texture = SubResource("GradientTexture2D_xknjj")

[node name="PointLight5" type="PointLight2D" parent="Light/LightAnimationPlayer"]
position = Vector2(1073, 45)
scale = Vector2(0.453125, 0.4375)
energy = 0.0
texture = SubResource("GradientTexture2D_xknjj")

[node name="WinLight4" type="PointLight2D" parent="Light/LightAnimationPlayer"]
position = Vector2(681, 46)
scale = Vector2(2.03125, 1.4375)
energy = 0.0
texture = SubResource("GradientTexture2D_xknjj")

[node name="WinLight5" type="PointLight2D" parent="Light/LightAnimationPlayer"]
position = Vector2(846, 16)
scale = Vector2(2.03125, 1.4375)
energy = 0.0
texture = SubResource("GradientTexture2D_xknjj")

[node name="FlowLight1" type="PointLight2D" parent="Light/LightAnimationPlayer"]
position = Vector2(1062, 173)
scale = Vector2(1.20313, 1.40625)
texture = SubResource("GradientTexture2D_syaut")

[node name="FlowLight2" type="PointLight2D" parent="Light/LightAnimationPlayer"]
position = Vector2(1144, 168)
scale = Vector2(1.20313, 1.40625)
texture = SubResource("GradientTexture2D_syaut")

[node name="Particles" type="Control" parent="."]
anchors_preset = 0
offset_right = 40.0
offset_bottom = 40.0

[node name="CollisionSparkParticles" parent="Particles" instance=ExtResource("9_fjlrm")]
self_modulate = Color(1, 1, 1, 0.607843)
position = Vector2(263, 272)
rotation = 3.14159
min_interval = 5.0
max_interval = 7.0

[node name="CollisionSparkParticles2" parent="Particles" instance=ExtResource("9_fjlrm")]
self_modulate = Color(1, 1, 1, 0.596078)
position = Vector2(140, 157)
min_interval = 3.0
max_interval = 5.0

[node name="FireflyParticles" parent="Particles" instance=ExtResource("10_xpdu6")]
modulate = Color(1, 1, 1, 0.7)
position = Vector2(576, 324)

[node name="MainMenu#Background" type="ColorRect" parent="."]
visible = false
material = SubResource("ShaderMaterial_17vd0")
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.2, 0.2, 0.4, 1)

[node name="MainMenu#Background2" type="ColorRect" parent="."]
visible = false
material = SubResource("ShaderMaterial_3buhr")
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.2, 0.2, 0.4, 1)

[node name="CenterContainer" type="CenterContainer" parent="."]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -489.0
offset_top = -200.0
offset_right = -139.0
offset_bottom = 200.0
grow_horizontal = 0
grow_vertical = 2

[node name="Panel" type="Panel" parent="CenterContainer"]
custom_minimum_size = Vector2(350, 450)
layout_mode = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_68j3p")

[node name="TextureRect" type="TextureRect" parent="CenterContainer/Panel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("7_xknjj")
expand_mode = 1
stretch_mode = 5

[node name="VBoxContainer" type="VBoxContainer" parent="CenterContainer"]
z_index = 30
custom_minimum_size = Vector2(200, 0)
layout_mode = 2
theme_override_constants/separation = 30

[node name="TitleContainer" type="Control" parent="CenterContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 60)
layout_mode = 2

[node name="TitleShadow" type="TextureRect" parent="CenterContainer/VBoxContainer/TitleContainer"]
visible = false
modulate = Color(0, 0, 0, 0.301961)
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 6.0
offset_top = 6.0
offset_right = 6.0
offset_bottom = 6.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(125, 30)
texture = ExtResource("8_xknjj")
expand_mode = 1
stretch_mode = 6

[node name="TitleImage" type="TextureRect" parent="CenterContainer/VBoxContainer/TitleContainer"]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(125, 30)
texture = ExtResource("8_xknjj")
expand_mode = 1
stretch_mode = 6

[node name="PointLight2D" type="PointLight2D" parent="CenterContainer/VBoxContainer/TitleContainer"]
position = Vector2(93, 1)
scale = Vector2(5.34375, 1.875)
texture = SubResource("GradientTexture2D_quvbe")

[node name="AnimationPlayer" type="AnimationPlayer" parent="CenterContainer/VBoxContainer/TitleContainer/PointLight2D"]
libraries = {
&"": SubResource("AnimationLibrary_4k4jm")
}
autoplay = "words_light"

[node name="StartButton" type="Button" parent="CenterContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 50)
layout_mode = 2
theme_override_font_sizes/font_size = 22
text = "开始游戏"

[node name="SettingsButton" type="Button" parent="CenterContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 50)
layout_mode = 2
theme_override_font_sizes/font_size = 22
text = "游戏设置"

[node name="CardCollectionButton" type="Button" parent="CenterContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 50)
layout_mode = 2
theme_override_font_sizes/font_size = 22
text = "卡牌收藏"

[node name="ExitButton" type="Button" parent="CenterContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 50)
layout_mode = 2
theme_override_font_sizes/font_size = 22
text = "退出游戏"

[node name="SettingsPanel" parent="." instance=ExtResource("4_17vd0")]
visible = false
z_index = 50
layout_mode = 1
