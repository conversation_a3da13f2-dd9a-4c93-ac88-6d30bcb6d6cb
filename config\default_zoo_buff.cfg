[buff_0]

type="tiger_buff"
name={
    "zh_CN": "老虎牌",
    "en": "Tiger Card",
    "ja": "虎カード",
    "ko": "호랑이 카드",
    "ru": "Карта тигра"
}
description_template={
    "zh_CN": "老虎: %d阶后+%d分, 每阶扣%d分",
    "en": "Tiger: +%d points after %d rounds, -%d points per round",
    "ja": "虎: %d段階後+%dポイント、毎段階-%dポイント",
    "ko": "호랑이: %d단계 후 +%d점, 매 단계 -%d점",
    "ru": "Тигр: +%d очков через %d этапов, -%d очков за этап"
}
display_name={
    "zh_CN": "凶猛攻式",
    "en": "Fierce Attack",
    "ja": "猛攻撃",
    "ko": "맹렬한 공격",
    "ru": "Свирепая атака"
}
explanation_template={
    "zh_CN": "[outline_size=4][outline_color=#000000][color=green]【猛虎扑食】[/color][/outline_color][/outline_size]: [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]-%d[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【大口吃肉】[/color][/outline_color][/outline_size]: [outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size] 回合后 [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]",
    "en": "[outline_size=4][outline_color=#000000][color=green]【Tiger Pounce】[/color][/outline_color][/outline_size]: [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]-%d[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【Big Bite】[/color][/outline_color][/outline_size]: After [outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size] rounds [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]",
    "ja": "[outline_size=4][outline_color=#000000][color=green]【猛虎襲撃】[/color][/outline_color][/outline_size]: [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]-%d[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【大口で食べる】[/color][/outline_color][/outline_size]: [outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size] ターン後 [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]",
    "ko": "[outline_size=4][outline_color=#000000][color=green]【맹호 습격】[/color][/outline_color][/outline_size]: [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]-%d[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【큰 입으로 먹기】[/color][/outline_color][/outline_size]: [outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size] 턴 후 [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]",
    "ru": "[outline_size=4][outline_color=#000000][color=green]【Тигриный прыжок】[/color][/outline_color][/outline_size]: [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]-%d[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【Большой укус】[/color][/outline_color][/outline_size]: Через [outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size] этапов [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]"
}
base_value_s=5
base_rounds_n=3
debuff_value=2

[buff_1]

type="rabbit_buff"
name={
    "zh_CN": "兔子牌",
    "en": "Rabbit Card",
    "ja": "兎カード",
    "ko": "토끼 카드",
    "ru": "Карта кролика"
}
description_template={
    "zh_CN": "兔子: 立刻+%d分, %d阶扣分",
    "en": "Rabbit: Instant +%d points, -%d points for %d rounds",
    "ja": "兎: 即座に+%dポイント、%d段階-%dポイント",
    "ko": "토끼: 즉시 +%d점, %d단계 동안 -%d점",
    "ru": "Кролик: мгновенно +%d очков, -%d очков за %d этапов"
}
display_name={
    "zh_CN": "乖乖还钱",
    "en": "Pay Back",
    "ja": "お返し",
    "ko": "갚기",
    "ru": "Расплата"
}
explanation_template={
    "zh_CN": "[outline_size=4][outline_color=#000000][color=green]【蹦蹦跳】[/color][/outline_color][/outline_size]: 一次性 [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【狡兔三窟】[/color][/outline_color][/outline_size]: [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]-%d[/color][/outline_color][/outline_size] 每回合",
    "en": "[outline_size=4][outline_color=#000000][color=green]【Hop Hop Jump】[/color][/outline_color][/outline_size]: One-time [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【Cunning Rabbit】[/color][/outline_color][/outline_size]: [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]-%d[/color][/outline_color][/outline_size] per round",
    "ja": "[outline_size=4][outline_color=#000000][color=green]【ぴょんぴょん跳び】[/color][/outline_color][/outline_size]: 一回限り [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【狡兎三窟】[/color][/outline_color][/outline_size]: [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]-%d[/color][/outline_color][/outline_size] 毎ターン",
    "ko": "[outline_size=4][outline_color=#000000][color=green]【깡충깡충 점프】[/color][/outline_color][/outline_size]: 일회성 [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【교활한 토끼】[/color][/outline_color][/outline_size]: [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]-%d[/color][/outline_color][/outline_size] 매 턴",
    "ru": "[outline_size=4][outline_color=#000000][color=green]【Прыг-скок】[/color][/outline_color][/outline_size]: Одноразово [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【Хитрый кролик】[/color][/outline_color][/outline_size]: [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]-%d[/color][/outline_color][/outline_size] за этап"
}
base_value_s=10
base_rounds_n=4
debuff_value=2

[buff_2]

type="frog_buff"
name={
    "zh_CN": "青蛙牌",
    "en": "Frog Card",
    "ja": "蛙カード",
    "ko": "개구리 카드",
    "ru": "Карта лягушки"
}
description_template={
    "zh_CN": "青蛙: %d阶上限 xN%, 禁用%s",
    "en": "Frog: %d rounds limit xN%, disable %s",
    "ja": "蛙: %d段階上限 xN%, %s無効",
    "ko": "개구리: %d단계 한계 xN%, %s 비활성화",
    "ru": "Лягушка: лимит %d этапов xN%, отключить %s"
}
display_name={
    "zh_CN": "跳出格",
    "en": "Jump Out",
    "ja": "枠から飛び出す",
    "ko": "틀에서 벗어나기",
    "ru": "Выпрыгнуть"
}
explanation_template={
    "zh_CN": "[outline_size=4][outline_color=#000000][color=green]【天鹅肉】[/color][/outline_color][/outline_size]: [img=32]res://assert/top/goal.png[/img] [outline_size=4][outline_color=#000000][color=%s]x%.1f[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【井底之蛙】[/color][/outline_color][/outline_size]: [color=red]禁用[/color] [color=%s]%s[/color] 牌",
    "en": "[outline_size=4][outline_color=#000000][color=green]【Swan Meat】[/color][/outline_color][/outline_size]: [img=32]res://assert/top/goal.png[/img] [outline_size=4][outline_color=#000000][color=%s]x%.1f[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【Frog in Well】[/color][/outline_color][/outline_size]: [color=red]Disable[/color] [color=%s]%s[/color] cards",
    "ja": "[outline_size=4][outline_color=#000000][color=green]【白鳥の肉】[/color][/outline_color][/outline_size]: [img=32]res://assert/top/goal.png[/img] [outline_size=4][outline_color=#000000][color=%s]x%.1f[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【井の中の蛙】[/color][/outline_color][/outline_size]: [color=red]無効[/color] [color=%s]%s[/color] カード",
    "ko": "[outline_size=4][outline_color=#000000][color=green]【백조 고기】[/color][/outline_color][/outline_size]: [img=32]res://assert/top/goal.png[/img] [outline_size=4][outline_color=#000000][color=%s]x%.1f[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【우물 안 개구리】[/color][/outline_color][/outline_size]: [color=red]비활성화[/color] [color=%s]%s[/color] 카드",
    "ru": "[outline_size=4][outline_color=#000000][color=green]【Лебединое мясо】[/color][/outline_color][/outline_size]: [img=32]res://assert/top/goal.png[/img] [outline_size=4][outline_color=#000000][color=%s]x%.1f[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【Лягушка в колодце】[/color][/outline_color][/outline_size]: [color=red]Отключить[/color] [color=%s]%s[/color] карты"
}
base_value_s=7
base_rounds_n=5
score_rate_min=0.6
score_rate_max=0.9

[buff_3]

type="turtle_buff"
name={
    "zh_CN": "乌龟牌",
    "en": "Turtle Card",
    "ja": "亀カード",
    "ko": "거북이 카드",
    "ru": "Карта черепахи"
}
description_template={
    "zh_CN": "乌龟: %s牌面值 x%d倍, %d阶后扣%d分",
    "en": "Turtle: %s card value x%d times, -%d points after %d rounds",
    "ja": "亀: %sカード値 x%d倍、%d段階後-%dポイント",
    "ko": "거북이: %s 카드 값 x%d배, %d단계 후 -%d점",
    "ru": "Черепаха: %s значение карты x%d раз, -%d очков через %d этапов"
}
display_name={
    "zh_CN": "慢慢来",
    "en": "Take It Slow",
    "ja": "ゆっくりと",
    "ko": "천천히",
    "ru": "Не торопись"
}
explanation_template={
    "zh_CN": "[outline_size=4][outline_color=#000000][color=green]【龟息功】[/color][/outline_color][/outline_size]: [color=%s]%s[/color] 牌面值 [outline_size=4][outline_color=#000000][color=%s]x%.1f[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【行动缓慢】[/color][/outline_color][/outline_size]: [outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size] 回合后 [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]-%d[/color][/outline_color][/outline_size]",
    "en": "[outline_size=4][outline_color=#000000][color=green]【Turtle Breathing】[/color][/outline_color][/outline_size]: [color=%s]%s[/color] card value [outline_size=4][outline_color=#000000][color=%s]x%.1f[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【Slow Movement】[/color][/outline_color][/outline_size]: After [outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size] rounds [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]-%d[/color][/outline_color][/outline_size]",
    "ja": "[outline_size=4][outline_color=#000000][color=green]【亀息功】[/color][/outline_color][/outline_size]: [color=%s]%s[/color] カード値 [outline_size=4][outline_color=#000000][color=%s]x%.1f[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【動作緩慢】[/color][/outline_color][/outline_size]: [outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size] ターン後 [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]-%d[/color][/outline_color][/outline_size]",
    "ko": "[outline_size=4][outline_color=#000000][color=green]【거북 호흡법】[/color][/outline_color][/outline_size]: [color=%s]%s[/color] 카드 값 [outline_size=4][outline_color=#000000][color=%s]x%.1f[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【느린 움직임】[/color][/outline_color][/outline_size]: [outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size] 턴 후 [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]-%d[/color][/outline_color][/outline_size]",
    "ru": "[outline_size=4][outline_color=#000000][color=green]【Черепашье дыхание】[/color][/outline_color][/outline_size]: [color=%s]%s[/color] значение карты [outline_size=4][outline_color=#000000][color=%s]x%.1f[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【Медленное движение】[/color][/outline_color][/outline_size]: Через [outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size] этапов [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]-%d[/color][/outline_color][/outline_size]"
}
base_value_s=2
base_rounds_n=3
penalty_value=10

[buff_4]

type="gorilla_buff"
name={
    "zh_CN": "猩猩牌",
    "en": "Gorilla Card",
    "ja": "ゴリラカード",
    "ko": "고릴라 카드",
    "ru": "Карта гориллы"
}
description_template={
    "zh_CN": "猩猩: 牌面超%d则+%d,否则-%d (%d阶)",
    "en": "Gorilla: If cards exceed %d then +%d, otherwise -%d (%d rounds)",
    "ja": "ゴリラ: カード値が%d超なら+%d、そうでなければ-%d (%d段階)",
    "ko": "고릴라: 카드가 %d를 초과하면 +%d, 그렇지 않으면 -%d (%d단계)",
    "ru": "Горилла: если карты больше %d, то +%d, иначе -%d (%d этапов)"
}
display_name={
    "zh_CN": "蛮力无双",
    "en": "Brute Force",
    "ja": "蛮力無双",
    "ko": "무력 무쌍",
    "ru": "Грубая сила"
}
explanation_template={
    "zh_CN": "[outline_size=4][outline_color=#000000][color=green]【猩猩之力】[/color][/outline_color][/outline_size]: 牌面值合大于 [outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size] 则 [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【猩猩低吼】[/color][/outline_color][/outline_size]: 牌面值合小于 [outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size] 则 [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]-%d[/color][/outline_color][/outline_size]",
    "en": "[outline_size=4][outline_color=#000000][color=green]【Gorilla Power】[/color][/outline_color][/outline_size]: If card total > [outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size] then [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【Gorilla Roar】[/color][/outline_color][/outline_size]: If card total < [outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size] then [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]-%d[/color][/outline_color][/outline_size]",
    "ja": "[outline_size=4][outline_color=#000000][color=green]【ゴリラの力】[/color][/outline_color][/outline_size]: カード合計が [outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size] より大きければ [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【ゴリラの咆哮】[/color][/outline_color][/outline_size]: カード合計が [outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size] より小さければ [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]-%d[/color][/outline_color][/outline_size]",
    "ko": "[outline_size=4][outline_color=#000000][color=green]【고릴라의 힘】[/color][/outline_color][/outline_size]: 카드 합계가 [outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size] 보다 크면 [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【고릴라 포효】[/color][/outline_color][/outline_size]: 카드 합계가 [outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size] 보다 작으면 [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]-%d[/color][/outline_color][/outline_size]",
    "ru": "[outline_size=4][outline_color=#000000][color=green]【Сила гориллы】[/color][/outline_color][/outline_size]: Если сумма карт > [outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size], то [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【Рев гориллы】[/color][/outline_color][/outline_size]: Если сумма карт < [outline_size=4][outline_color=#000000][color=%s]%d[/color][/outline_color][/outline_size], то [img=32]res://assert/top/score.png[/img] [outline_size=4][outline_color=#000000][color=%s]-%d[/color][/outline_color][/outline_size]"
}
base_value_s=15
base_rounds_n=3
base_penalty_n=3

[buff_5]

type="shark_buff"
name={
    "zh_CN": "鲨鱼牌",
    "en": "Shark Card",
    "ja": "サメカード",
    "ko": "상어 카드",
    "ru": "Карта акулы"
}
description_template={
    "zh_CN": "鲨鱼: 所有卡牌效果阶+%d, 青蛙和乌龟牌失效",
    "en": "Shark: All card effects +%d rounds, Frog and Turtle cards disabled",
    "ja": "サメ: 全カード効果段階+%d、蛙と亀カード無効",
    "ko": "상어: 모든 카드 효과 +%d단계, 개구리와 거북이 카드 비활성화",
    "ru": "Акула: все эффекты карт +%d этапов, карты лягушки и черепахи отключены"
}
display_name={
    "zh_CN": "疯狂鲨戮",
    "en": "Shark Frenzy",
    "ja": "狂気のサメ",
    "ko": "광란의 상어",
    "ru": "Акулье безумие"
}
explanation_template={
    "zh_CN": "[outline_size=4][outline_color=#000000][color=green]【鱼翅奖励】[/color][/outline_color][/outline_size]: 所有卡牌效果阶数 [outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【鲨杀杀】[/color][/outline_color][/outline_size]: [outline_size=4][outline_color=#000000][color=%s]%s[/color][/outline_color][/outline_size] 立即失效",
    "en": "[outline_size=4][outline_color=#000000][color=green]【Shark Fin Reward】[/color][/outline_color][/outline_size]: All card effect rounds [outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【Shark Attack】[/color][/outline_color][/outline_size]: [outline_size=4][outline_color=#000000][color=%s]%s[/color][/outline_color][/outline_size] immediately disabled",
    "ja": "[outline_size=4][outline_color=#000000][color=green]【フカヒレ報酬】[/color][/outline_color][/outline_size]: 全カード効果段階 [outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【サメ殺し】[/color][/outline_color][/outline_size]: [outline_size=4][outline_color=#000000][color=%s]%s[/color][/outline_color][/outline_size] 即座に無効",
    "ko": "[outline_size=4][outline_color=#000000][color=green]【상어 지느러미 보상】[/color][/outline_color][/outline_size]: 모든 카드 효과 단계 [outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【상어 공격】[/color][/outline_color][/outline_size]: [outline_size=4][outline_color=#000000][color=%s]%s[/color][/outline_color][/outline_size] 즉시 비활성화",
    "ru": "[outline_size=4][outline_color=#000000][color=green]【Акулий плавник】[/color][/outline_color][/outline_size]: Все этапы эффектов карт [outline_size=4][outline_color=#000000][color=%s]+%d[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【Акулья атака】[/color][/outline_color][/outline_size]: [outline_size=4][outline_color=#000000][color=%s]%s[/color][/outline_color][/outline_size] немедленно отключены"
}
base_value_s=2
base_rounds_n=1

[buff_6]

type="eagle_buff"
name={
    "zh_CN": "老鹰牌",
    "en": "Eagle Card",
    "ja": "鷲カード",
    "ko": "독수리 카드",
    "ru": "Карта орла"
}
description_template={
    "zh_CN": "老鹰: 兔子牌失效, 老虎/猩猩牌阶×%d",
    "en": "Eagle: Rabbit cards disabled, Tiger/Gorilla card rounds ×%d",
    "ja": "鷲: 兎カード無効、虎/ゴリラカード段階×%d",
    "ko": "독수리: 토끼 카드 비활성화, 호랑이/고릴라 카드 단계 ×%d",
    "ru": "Орел: карты кролика отключены, этапы карт тигра/гориллы ×%d"
}
display_name={
    "zh_CN": "鹰击长空",
    "en": "Eagle Strike",
    "ja": "鷲の一撃",
    "ko": "독수리의 일격",
    "ru": "Удар орла"
}
explanation_template={
    "zh_CN": "[outline_size=4][outline_color=#000000][color=green]【大鹏展翅】[/color][/outline_color][/outline_size]: [outline_size=4][outline_color=#000000][color=%s]%s[/color][/outline_color][/outline_size] 剩余效果阶数 [outline_size=4][outline_color=#000000][color=%s]x%d[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【直击灵魂】[/color][/outline_color][/outline_size]: [outline_size=4][outline_color=#000000][color=%s]%s[/color][/outline_color][/outline_size] 立即失效",
    "en": "[outline_size=4][outline_color=#000000][color=green]【Eagle Spread Wings】[/color][/outline_color][/outline_size]: [outline_size=4][outline_color=#000000][color=%s]%s[/color][/outline_color][/outline_size] remaining effect rounds [outline_size=4][outline_color=#000000][color=%s]x%d[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【Soul Strike】[/color][/outline_color][/outline_size]: [outline_size=4][outline_color=#000000][color=%s]%s[/color][/outline_color][/outline_size] immediately disabled",
    "ja": "[outline_size=4][outline_color=#000000][color=green]【大鵬展翅】[/color][/outline_color][/outline_size]: [outline_size=4][outline_color=#000000][color=%s]%s[/color][/outline_color][/outline_size] 残り効果段階 [outline_size=4][outline_color=#000000][color=%s]x%d[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【魂直撃】[/color][/outline_color][/outline_size]: [outline_size=4][outline_color=#000000][color=%s]%s[/color][/outline_color][/outline_size] 即座に無効",
    "ko": "[outline_size=4][outline_color=#000000][color=green]【대붕전시】[/color][/outline_color][/outline_size]: [outline_size=4][outline_color=#000000][color=%s]%s[/color][/outline_color][/outline_size] 남은 효과 단계 [outline_size=4][outline_color=#000000][color=%s]x%d[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【영혼 직격】[/color][/outline_color][/outline_size]: [outline_size=4][outline_color=#000000][color=%s]%s[/color][/outline_color][/outline_size] 즉시 비활성화",
    "ru": "[outline_size=4][outline_color=#000000][color=green]【Орлиный размах】[/color][/outline_color][/outline_size]: [outline_size=4][outline_color=#000000][color=%s]%s[/color][/outline_color][/outline_size] оставшиеся этапы эффектов [outline_size=4][outline_color=#000000][color=%s]x%d[/color][/outline_color][/outline_size]\n[outline_size=4][outline_color=#000000][color=red]【Удар в душу】[/color][/outline_color][/outline_size]: [outline_size=4][outline_color=#000000][color=%s]%s[/color][/outline_color][/outline_size] немедленно отключены"
}
base_value_s=2
base_rounds_n=1
