[gd_scene load_steps=4 format=3 uid="uid://bxnv6cucb8e77"]

[ext_resource type="Script" uid="uid://d4b1jccesmrrm" path="res://script/steam_connection_dialog.gd" id="1_steam_dialog"]
[ext_resource type="Theme" uid="uid://c5v7o2hfj6n4w" path="res://themes/game_theme.tres" id="2_theme"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_panel"]
bg_color = Color(0.2, 0.2, 0.2, 0.9)
corner_radius_top_left = 25
corner_radius_top_right = 25
corner_radius_bottom_right = 25
corner_radius_bottom_left = 25
shadow_color = Color(0, 0, 0, 0.5)
shadow_size = 15
shadow_offset = Vector2(2, 2)

[node name="SteamConnectionDialog" type="Control"]
z_index = 100
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("2_theme")
script = ExtResource("1_steam_dialog")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0, 0, 0, 0.6)

[node name="CenterContainer" type="CenterContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="DialogPanel" type="Panel" parent="CenterContainer"]
custom_minimum_size = Vector2(400, 300)
layout_mode = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_panel")

[node name="VBoxContainer" type="VBoxContainer" parent="CenterContainer/DialogPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 2

[node name="TitleLabel" type="Label" parent="CenterContainer/DialogPanel/VBoxContainer"]
layout_mode = 2
text = "Steam连接失败"
horizontal_alignment = 1
vertical_alignment = 1

[node name="HSeparator" type="HSeparator" parent="CenterContainer/DialogPanel/VBoxContainer"]
modulate = Color(1, 1, 1, 0.3)
layout_mode = 2

[node name="MessageLabel" type="Label" parent="CenterContainer/DialogPanel/VBoxContainer"]
layout_mode = 2
text = "未连接上Steam，请检查Steam是否正在运行"
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 3

[node name="VSeparator" type="Control" parent="CenterContainer/DialogPanel/VBoxContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="ButtonContainer" type="HBoxContainer" parent="CenterContainer/DialogPanel/VBoxContainer"]
layout_mode = 2
alignment = 1

[node name="ReconnectButton" type="Button" parent="CenterContainer/DialogPanel/VBoxContainer/ButtonContainer"]
custom_minimum_size = Vector2(120, 40)
layout_mode = 2
text = "重新连接"

[node name="ExitButton" type="Button" parent="CenterContainer/DialogPanel/VBoxContainer/ButtonContainer"]
custom_minimum_size = Vector2(120, 40)
layout_mode = 2
text = "退出"

[node name="LoadingContainer" type="HBoxContainer" parent="CenterContainer/DialogPanel/VBoxContainer"]
visible = false
layout_mode = 2
alignment = 1

[node name="LoadingSpinner" type="Label" parent="CenterContainer/DialogPanel/VBoxContainer/LoadingContainer"]
layout_mode = 2
text = "连接中..."
horizontal_alignment = 1
