shader_type canvas_item;

// 效果开关
uniform bool enable_float_effect = true;       // 浮动效果开关
uniform bool enable_3d_effect = false;         // 3D效果开关（中心卡牌时启用）
uniform bool enable_silhouette_effect = false; // 剪影效果开关（未解锁卡牌）
uniform bool enable_shine_effect = false;      // 闪光效果开关（已解锁卡牌）

// 传递浮动偏移给fragment shader
varying vec2 float_offset;

// ===== 浮动效果参数 =====
uniform float float_amplitude : hint_range(0.0, 10.0) = 4.5;  // 浮动幅度
uniform float float_frequency : hint_range(0.1, 5.0) = 0.8;   // 浮动频率
uniform float time_offset = 0.0;                            // 时间偏移，用于错开多个卡牌的浮动

// ===== 3D效果参数 =====
uniform float card_width = 120.0;              // 卡牌宽度
uniform float card_height = 180.0;             // 卡牌高度
uniform vec2 mouse_position = vec2(0.0, 0.0);  // 鼠标位置（相对于卡牌中心）
uniform float tilt_strength : hint_range(0.0, 1.0) = 0.25;  // 3D倾斜强度

// ===== 卡牌内容纹理 =====
uniform sampler2D card_icon;                   // 卡牌图标纹理（zoo卡牌使用）
uniform sampler2D lock_icon;                   // 锁定图标纹理
uniform sampler2D card_name_texture;           // 卡牌名称纹理
uniform bool show_icon = true;                 // 是否显示图标
uniform bool show_lock = false;                // 是否显示锁定图标
uniform bool show_name = true;                 // 是否显示名称

// ===== 图标和锁定图标位置参数 =====
uniform vec4 icon_rect = vec4(0.2, 0.15, 0.8, 0.65);     // 图标区域 (left, top, right, bottom)
uniform vec4 lock_rect = vec4(0.4, 0.35, 0.6, 0.65);      // 锁定图标区域
uniform vec4 name_rect = vec4(0.1, 0.77, 0.9, 1.0);      // 名称区域 (left, top, right, bottom)

// ===== 图标效果参数 =====
uniform vec4 icon_tint_color : source_color = vec4(0.6, 0.3, 0.8, 1.0);  // 图标紫色着色
uniform float icon_glow_intensity : hint_range(0.0, 1.0) = 0.3;           // 图标发光强度
uniform float icon_glow_radius : hint_range(0.0, 0.1) = 0.05;             // 图标发光半径

// ===== 剪影效果参数 =====
uniform vec4 silhouette_color : source_color = vec4(0.0, 0.0, 0.0, 1.0);  // 剪影颜色

// ===== 边缘模糊效果参数 =====
uniform bool enable_edge_blur = true;                                      // 边缘模糊开关
uniform float edge_blur_radius : hint_range(0.0, 5.0) = 2;              // 模糊半径

// ===== 闪光效果参数 =====
uniform float shine_line_smoothness : hint_range(0, 0.1) = 0.05;
uniform float shine_line_width : hint_range(0, 0.2) = 0.1;
uniform float shine_brightness = 2.0;
uniform float shine_rotation_deg : hint_range(-90, 90) = 30;
uniform float shine_distortion : hint_range(1, 2) = 1.6;
uniform float shine_speed = 0.3;
uniform float shine_position : hint_range(0, 1) = 0;
uniform float shine_position_min = 0.2;
uniform float shine_position_max = 0.6;
uniform float shine_alpha : hint_range(0, 1) = 0.7;
uniform float shine_corner_radius : hint_range(0.0, 0.5) = 0.12;
uniform float shine_edge_smoothness : hint_range(0.0, 0.1) = 0.01;

void vertex() {
    float_offset = vec2(0.0, 0.0);

    // 浮动效果
    if (enable_float_effect) {
        float time = TIME * float_frequency + time_offset;

        // Y轴浮动
        float y_offset = sin(time) * float_amplitude;
        VERTEX.y += y_offset;

        // 轻微的X轴摆动
        float x_offset = sin(time * 1.3 + 0.5) * float_amplitude * 0.3;
        VERTEX.x += x_offset;

        // 记录浮动偏移，用于名称标签同步
        float_offset = vec2(x_offset, y_offset);

        // 轻微的旋转效果
        float rotation = sin(time * 0.7) * 0.008; // 非常轻微的旋转
        vec2 center = vec2(card_width * 0.5, card_height * 0.5);
        vec2 centered_vertex = VERTEX - center;
        float s = sin(rotation);
        float c = cos(rotation);
        VERTEX.x = centered_vertex.x * c - centered_vertex.y * s + center.x;
        VERTEX.y = centered_vertex.x * s + centered_vertex.y * c + center.y;
    }
}

// 在指定区域内绘制纹理的辅助函数
vec4 draw_texture_in_rect(sampler2D tex, vec2 uv, vec4 rect) {
    // 检查UV是否在指定矩形内
    if (uv.x >= rect.x && uv.x <= rect.z && uv.y >= rect.y && uv.y <= rect.w) {
        // 将UV映射到纹理坐标
        vec2 tex_uv = vec2(
            (uv.x - rect.x) / (rect.z - rect.x),
            (uv.y - rect.y) / (rect.w - rect.y)
        );
        return texture(tex, tex_uv);
    }
    return vec4(0.0);
}

// 旋转UV坐标的辅助函数
vec2 rotate_uv(vec2 uv, vec2 center, float rotation, bool use_degrees){
    float _angle = rotation;
    if(use_degrees){
        _angle = rotation * (3.1415926/180.0);
    }
    mat2 _rotation = mat2(
        vec2(cos(_angle), -sin(_angle)),
        vec2(sin(_angle), cos(_angle))
    );
    vec2 _delta = uv - center;
    _delta = _rotation * _delta;
    return _delta + center;
}

// 计算闪光效果
float calculate_shine_effect(vec2 uv) {
    vec2 center_uv = uv - vec2(0.5, 0.5);
    float gradient_to_edge = max(abs(center_uv.x), abs(center_uv.y));
    gradient_to_edge = gradient_to_edge * shine_distortion;
    gradient_to_edge = 1.0 - gradient_to_edge;
    vec2 rotated_uv = rotate_uv(uv, vec2(0.5, 0.5), shine_rotation_deg, true);

    float remapped_position;
    {
        float output_range = shine_position_max - shine_position_min;
        remapped_position = shine_position_min + output_range * shine_position;
    }

    float remapped_time = TIME * shine_speed + remapped_position;
    remapped_time = fract(remapped_time);
    {
        remapped_time = -2.0 + 4.0 * remapped_time;
    }

    vec2 offset_uv = vec2(rotated_uv.xy) + vec2(remapped_time, 0.0);
    float line = vec3(offset_uv, 0.0).x;
    line = abs(line);
    line = gradient_to_edge * line;
    line = sqrt(line);

    float line_smoothness = clamp(shine_line_smoothness, 0.001, 1.0);
    float offset_plus = shine_line_width + line_smoothness;
    float offset_minus = shine_line_width - line_smoothness;

    float remapped_line;
    {
        float input_range = offset_minus - offset_plus;
        remapped_line = (line - offset_plus) / input_range;
    }
    remapped_line *= shine_brightness;
    remapped_line = min(remapped_line, shine_alpha);

    // 计算圆角蒙版
    vec2 center_distance = abs(uv - vec2(0.5));
    float corner_distance = length(max(center_distance - vec2(0.5 - shine_corner_radius), vec2(0.0)));
    float corner_mask = 1.0 - smoothstep(shine_corner_radius - shine_edge_smoothness, shine_corner_radius + shine_edge_smoothness, corner_distance);

    return remapped_line * corner_mask;
}

void fragment() {
    vec2 final_uv = UV;
    vec4 final_color = texture(TEXTURE, UV);

    // 3D效果处理 - 使用与card_detail_panel相同的实现
    if (enable_3d_effect) {
        // map skew to [-0.5, 0.5]
        float skew_x = mouse_position.x / card_width;
        float skew_y = mouse_position.y / card_height;

        // map to [-0.5, 0.5]
        vec2 uv = UV;
        uv.x = (uv.x - 0.5);
        uv.y = (uv.y - 0.5);

        // ratio - how far are current point from mouse position
        float sx = 1.0 - (uv.x * skew_x);
        float sy = 1.0 - (uv.y * skew_y);

        // calculate z (depth) depending on ratio
        float z = 1.0 + (sx * sy) / 2.0;
        // correct perspective for given point
        uv.x = uv.x / z;
        uv.y = uv.y / z;

        // scale a bit down and reset mapping from [-0.5, 0.5] to [0, 1]
        uv.x = uv.x / 0.45 + 0.5;
        uv.y = uv.y / 0.45 + 0.5;

        // 如果uv超出范围，设置为透明
        if (uv.x < 0.0 || uv.x > 1.0 || uv.y < 0.0 || uv.y > 1.0) {
            final_color.a = 0.0;
        } else {
            // 应用亮度调整
            float brightness = 1.0 - mouse_position.y / (card_height / 2.0) * 0.2;
            final_color = texture(TEXTURE, uv);
            final_color.rgb *= brightness;
        }

        // 更新UV坐标用于后续效果
        final_uv = uv;
    }

    // 应用剪影效果（未解锁卡牌）
    if (enable_silhouette_effect) {
        if (final_color.a > 0.0) {
            final_color = vec4(silhouette_color.rgb, final_color.a * silhouette_color.a);
        }
    }

    // 绘制图标（仅在解锁且显示图标时）
    if (show_icon && !enable_silhouette_effect) {
        vec4 icon_color = draw_texture_in_rect(card_icon, final_uv, icon_rect);
        if (icon_color.a > 0.0) {
            // 应用紫色着色
            vec3 tinted_icon = icon_color.rgb * icon_tint_color.rgb;

            // 计算发光效果
            vec2 icon_center = (icon_rect.xy + icon_rect.zw) * 0.5;
            float dist_to_icon_center = length(final_uv - icon_center);
            vec2 icon_size = icon_rect.zw - icon_rect.xy;
            float icon_radius = length(icon_size) * 0.5;

            // 创建发光区域
            float glow_falloff = smoothstep(icon_radius + icon_glow_radius, icon_radius, dist_to_icon_center);
            vec3 glow_color = icon_tint_color.rgb * icon_glow_intensity * glow_falloff;

            // 在图标区域外添加发光效果
            if (dist_to_icon_center > icon_radius) {
                final_color.rgb += glow_color;
            }

            // 混合着色后的图标
            final_color.rgb = mix(final_color.rgb, tinted_icon + glow_color * 0.5, icon_color.a);
            final_color.a = max(final_color.a, icon_color.a);
        }
    }

    // 绘制锁定图标（仅在显示锁定时）
    if (show_lock) {
        vec4 lock_color = draw_texture_in_rect(lock_icon, final_uv, lock_rect);
        if (lock_color.a > 0.0) {
            // 使用alpha混合
            final_color.rgb = mix(final_color.rgb, lock_color.rgb, lock_color.a);
            final_color.a = max(final_color.a, lock_color.a);
        }
    }

    // 绘制卡牌名称（仅在解锁且显示名称时）
    if (show_name && !enable_silhouette_effect) {
        vec4 name_color = draw_texture_in_rect(card_name_texture, final_uv, name_rect);
        if (name_color.a > 0.0) {
            // 使用alpha混合，并添加阴影效果
            final_color.rgb = mix(final_color.rgb, name_color.rgb, name_color.a);
            final_color.a = max(final_color.a, name_color.a);
        }
    }

    // 应用边缘模糊效果
    if (enable_edge_blur) {
        // 基于原始纹理形状进行边缘检测
        vec2 texel_size = 1.0 / vec2(card_width, card_height);

        // 采样周围像素的alpha值
        float alpha_sum = 0.0;
        float sample_count = 0.0;

        for (float x = -1.0; x <= 1.0; x += 1.0) {
            for (float y = -1.0; y <= 1.0; y += 1.0) {
                vec2 sample_uv = final_uv + vec2(x, y) * texel_size * edge_blur_radius;
                if (sample_uv.x >= 0.0 && sample_uv.x <= 1.0 && sample_uv.y >= 0.0 && sample_uv.y <= 1.0) {
                    alpha_sum += texture(TEXTURE, sample_uv).a;
                    sample_count += 1.0;
                }
            }
        }

        // 计算平均alpha值作为模糊因子
        if (sample_count > 0.0) {
            float avg_alpha = alpha_sum / sample_count;
            final_color.a = mix(final_color.a, avg_alpha, 0.3);
        }
    }

    // 应用闪光效果（仅在已解锁且启用时）
    if (enable_shine_effect && !enable_silhouette_effect) {
        float shine = calculate_shine_effect(final_uv);
        if (shine > 0.0) {
            // 添加白色闪光效果
            final_color.rgb += vec3(shine) * final_color.a;
        }
    }

    COLOR = final_color;
}
