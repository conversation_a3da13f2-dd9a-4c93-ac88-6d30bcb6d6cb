[gd_scene load_steps=12 format=3 uid="uid://bhd0lqeb1ab75"]

[ext_resource type="Script" uid="uid://ck0gt3h2p8hfd" path="res://script/game_over_panel.gd" id="1_qqvqj"]
[ext_resource type="Shader" uid="uid://bawkvu87t8s5n" path="res://shader/dice_panel_shader.gdshader" id="2_r027g"]
[ext_resource type="Texture2D" uid="uid://c3e5ad50fb1vf" path="res://assert/game_over_panel/back03.png" id="3_w8gho"]
[ext_resource type="Texture2D" uid="uid://b8reqp3w4cm7v" path="res://assert/game_over_panel/restart_button.png" id="4_f0myb"]
[ext_resource type="Texture2D" uid="uid://d2ufdha18ne10" path="res://assert/game_over_panel/cicle2.png" id="4_w8gho"]
[ext_resource type="Texture2D" uid="uid://bsrkxcj8mqkbg" path="res://assert/game_over_panel/return_button.png" id="5_w8gho"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_uwb3g"]
bg_color = Color(0.6, 0.6, 0.6, 0)

[sub_resource type="Gradient" id="Gradient_uwb3g"]
colors = PackedColorArray(0, 0, 0, 1, 1, 0, 1, 1)

[sub_resource type="GradientTexture2D" id="GradientTexture2D_t0f64"]
gradient = SubResource("Gradient_uwb3g")
fill = 1
fill_from = Vector2(0.5, 0.5)

[sub_resource type="ShaderMaterial" id="ShaderMaterial_g5m6w"]
shader = ExtResource("2_r027g")
shader_parameter/gradient = SubResource("GradientTexture2D_t0f64")
shader_parameter/spread = 0.51
shader_parameter/cutoff = 2.0
shader_parameter/size = 0.54
shader_parameter/speed = 1.0
shader_parameter/ray1_density = 10.0
shader_parameter/ray2_density = 20.0
shader_parameter/ray2_intensity = 0.8
shader_parameter/core_intensity = -0.945
shader_parameter/hdr = false
shader_parameter/seed = 6.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_slot"]
bg_color = Color(1, 1, 1, 0)
border_width_left = 10
border_width_top = 10
border_width_right = 10
border_width_bottom = 10
border_color = Color(1, 0.533333, 0, 0)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10
anti_aliasing_size = 0.01

[node name="GameOverPanel" type="Panel"]
z_index = 50
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -300.0
offset_right = 200.0
offset_bottom = 300.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_uwb3g")
script = ExtResource("1_qqvqj")

[node name="Shine" type="ColorRect" parent="."]
show_behind_parent = true
material = SubResource("ShaderMaterial_g5m6w")
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -568.0
offset_top = -536.0
offset_right = 568.0
offset_bottom = 528.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(578, 317)

[node name="TextureRect" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("3_w8gho")
expand_mode = 1
stretch_mode = 5

[node name="VBoxContainer" type="Control" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_bottom = 19.0
grow_horizontal = 2
grow_vertical = 2

[node name="GameOverTitle" type="Label" parent="VBoxContainer"]
layout_mode = 2
offset_top = 35.0
offset_right = 400.0
offset_bottom = 80.0
theme_override_colors/font_color = Color(1, 0.8, 0, 1)
theme_override_font_sizes/font_size = 32
text = "GameOverTitle"
horizontal_alignment = 1
vertical_alignment = 1

[node name="SlotMachineContainer" type="Panel" parent="VBoxContainer"]
custom_minimum_size = Vector2(0, 200)
layout_mode = 2
offset_left = 32.0
offset_top = 128.0
offset_right = 368.0
offset_bottom = 335.0
theme_override_styles/panel = SubResource("StyleBoxFlat_slot")

[node name="SlotViewport" type="SubViewportContainer" parent="VBoxContainer/SlotMachineContainer"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -100.0
offset_top = -110.5
offset_right = 100.0
offset_bottom = 93.5
grow_horizontal = 2
grow_vertical = 2
stretch = true

[node name="SubViewport" type="SubViewport" parent="VBoxContainer/SlotMachineContainer/SlotViewport"]
transparent_bg = true
handle_input_locally = false
size = Vector2i(200, 204)
render_target_update_mode = 4

[node name="SlotItems" type="Control" parent="VBoxContainer/SlotMachineContainer/SlotViewport/SubViewport"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_bottom = 13.0
grow_horizontal = 2
grow_vertical = 2

[node name="Sprite1" type="Sprite2D" parent="VBoxContainer/SlotMachineContainer/SlotViewport/SubViewport/SlotItems"]
position = Vector2(100, -268)
scale = Vector2(0.15, 0.15)

[node name="Sprite2" type="Sprite2D" parent="VBoxContainer/SlotMachineContainer/SlotViewport/SubViewport/SlotItems"]
position = Vector2(100, -76)
scale = Vector2(0.15, 0.15)

[node name="Sprite3" type="Sprite2D" parent="VBoxContainer/SlotMachineContainer/SlotViewport/SubViewport/SlotItems"]
position = Vector2(100, 116)
scale = Vector2(0.15, 0.15)

[node name="Sprite4" type="Sprite2D" parent="VBoxContainer/SlotMachineContainer/SlotViewport/SubViewport/SlotItems"]
position = Vector2(100, 308)
scale = Vector2(0.15, 0.15)

[node name="Sprite5" type="Sprite2D" parent="VBoxContainer/SlotMachineContainer/SlotViewport/SubViewport/SlotItems"]
position = Vector2(100, 500)
scale = Vector2(0.15, 0.15)

[node name="Mask" type="TextureRect" parent="VBoxContainer"]
layout_mode = 0
offset_left = 39.0
offset_top = 76.0
offset_right = 372.0
offset_bottom = 367.0
texture = ExtResource("4_w8gho")
expand_mode = 1
stretch_mode = 5

[node name="ResultLabel" type="RichTextLabel" parent="VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
offset_top = 371.0
offset_right = 400.0
offset_bottom = 411.0
theme_override_font_sizes/normal_font_size = 15
theme_override_font_sizes/bold_font_size = 15
bbcode_enabled = true
text = "您被制作成了..."
fit_content = true
scroll_active = false
shortcut_keys_enabled = false
horizontal_alignment = 1
vertical_alignment = 1

[node name="ScoreLabel" type="Label" parent="VBoxContainer"]
visible = false
layout_mode = 2
offset_top = 415.0
offset_right = 400.0
offset_bottom = 449.0
theme_override_font_sizes/font_size = 24
text = "最终积分: 0 / 0"
horizontal_alignment = 1
vertical_alignment = 1

[node name="HBoxContainer" type="HBoxContainer" parent="VBoxContainer"]
layout_mode = 0
offset_left = 32.0
offset_top = 407.0
offset_right = 380.0
offset_bottom = 447.0
alignment = 1

[node name="RecordLabel" type="Label" parent="VBoxContainer/HBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 17
text = "RecordLabel"
horizontal_alignment = 1
vertical_alignment = 1

[node name="LeveRoundlLabel" type="Label" parent="VBoxContainer/HBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 17
text = "关卡： 1  /  阶: 1"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ButtonsContainer" type="Control" parent="VBoxContainer"]
layout_mode = 2
anchors_preset = 0
offset_left = 32.0
offset_top = 491.0
offset_right = 368.0
offset_bottom = 527.0

[node name="RestartButton" type="Button" parent="VBoxContainer/ButtonsContainer"]
layout_mode = 0
offset_left = 93.0
offset_top = -6.0
offset_right = 131.0
offset_bottom = 25.0
flat = true

[node name="Shadow" type="TextureRect" parent="VBoxContainer/ButtonsContainer/RestartButton"]
modulate = Color(0, 0, 0, 0.372549)
layout_mode = 0
offset_left = -14.0
offset_top = -11.0
offset_right = 58.0
offset_bottom = 61.0
texture = ExtResource("4_f0myb")
expand_mode = 1
stretch_mode = 5

[node name="TextureRect" type="TextureRect" parent="VBoxContainer/ButtonsContainer/RestartButton"]
layout_mode = 0
offset_left = -17.0
offset_top = -13.0
offset_right = 55.0
offset_bottom = 59.0
texture = ExtResource("4_f0myb")
expand_mode = 1
stretch_mode = 5

[node name="Mask" type="TextureRect" parent="VBoxContainer/ButtonsContainer/RestartButton"]
modulate = Color(0, 0, 0, 0.333333)
layout_mode = 0
offset_left = -8.0
offset_top = -6.0
offset_right = 47.0
offset_bottom = 46.0
texture = ExtResource("4_f0myb")
expand_mode = 1
stretch_mode = 5

[node name="MainMenuButton" type="Button" parent="VBoxContainer/ButtonsContainer"]
layout_mode = 2
offset_left = 215.0
offset_top = -1.0
offset_right = 253.0
offset_bottom = 24.0
theme_override_font_sizes/font_size = 20
flat = true

[node name="Shadow" type="TextureRect" parent="VBoxContainer/ButtonsContainer/MainMenuButton"]
modulate = Color(0, 0, 0, 0.364706)
layout_mode = 0
offset_left = -15.0
offset_top = -8.0
offset_right = 57.0
offset_bottom = 48.0
texture = ExtResource("5_w8gho")
expand_mode = 1
stretch_mode = 5

[node name="TextureRect" type="TextureRect" parent="VBoxContainer/ButtonsContainer/MainMenuButton"]
layout_mode = 0
offset_left = -17.0
offset_top = -10.0
offset_right = 55.0
offset_bottom = 46.0
texture = ExtResource("5_w8gho")
expand_mode = 1
stretch_mode = 5
flip_h = true

[node name="Mask" type="TextureRect" parent="VBoxContainer/ButtonsContainer/MainMenuButton"]
modulate = Color(0, 0, 0, 0.321569)
layout_mode = 0
offset_left = -6.0
offset_top = -6.0
offset_right = 44.0
offset_bottom = 37.0
texture = ExtResource("5_w8gho")
expand_mode = 1
stretch_mode = 5

[node name="BottomSpacer" type="Control" parent="VBoxContainer"]
layout_mode = 2
anchors_preset = 0
offset_top = 619.0
offset_right = 400.0
offset_bottom = 619.0
size_flags_vertical = 3
size_flags_stretch_ratio = 0.5
