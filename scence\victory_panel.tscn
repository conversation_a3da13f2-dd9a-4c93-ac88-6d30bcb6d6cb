[gd_scene load_steps=11 format=3 uid="uid://q8gc6kybbkr3"]

[ext_resource type="Script" uid="uid://dnva6bhg0teww" path="res://script/victory_panel.gd" id="1_script"]
[ext_resource type="FontFile" uid="uid://cdnwx7btcvfpe" path="res://fonts/siyuan.ttf" id="2_qgr56"]
[ext_resource type="Shader" uid="uid://bawkvu87t8s5n" path="res://shader/dice_panel_shader.gdshader" id="3_fqkcu"]
[ext_resource type="Texture2D" uid="uid://co4oag7nn0eta" path="res://themes/victory_panel_back.tres" id="3_uni7q"]
[ext_resource type="Texture2D" uid="uid://dukicgn7re65h" path="res://assert/victory_panel/back02.png" id="4_fqkcu"]
[ext_resource type="Texture2D" uid="uid://db21axqqm5et6" path="res://assert/victory_panel/next_button02.png" id="5_mk84x"]
[ext_resource type="Texture2D" uid="uid://h1d0qqdrq48s" path="res://assert/items/duck_speaker.png" id="5_y3dxj"]
[ext_resource type="Texture2D" uid="uid://bsv66s0h1yugm" path="res://assert/items/duck_good.png" id="6_mk84x"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_qgr56"]
shader = ExtResource("3_fqkcu")
shader_parameter/gradient = ExtResource("3_uni7q")
shader_parameter/spread = 0.46
shader_parameter/cutoff = 2.0
shader_parameter/size = 0.815
shader_parameter/speed = 1.0
shader_parameter/ray1_density = 8.0
shader_parameter/ray2_density = 30.0
shader_parameter/ray2_intensity = 1.0
shader_parameter/core_intensity = 2.0
shader_parameter/hdr = false
shader_parameter/seed = 10.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_mk84x"]
bg_color = Color(0.6, 0.6, 0.6, 0)
border_color = Color(0.8, 0.8, 0.8, 0)

[node name="VictoryPanel" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_script")

[node name="ColorRect" type="ColorRect" parent="."]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0, 0, 0, 0.5)

[node name="Shine2" type="ColorRect" parent="."]
show_behind_parent = true
material = SubResource("ShaderMaterial_qgr56")
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -384.0
offset_top = -320.0
offset_right = 384.0
offset_bottom = 320.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(578, 317)
mouse_filter = 2

[node name="CenterContainer" type="CenterContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="Panel" type="Panel" parent="CenterContainer"]
custom_minimum_size = Vector2(600, 400)
layout_mode = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_mk84x")

[node name="BackGround" type="TextureRect" parent="CenterContainer/Panel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("4_fqkcu")
expand_mode = 1
stretch_mode = 5

[node name="VBoxContainer" type="Control" parent="CenterContainer/Panel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="TitleLabel" type="RichTextLabel" parent="CenterContainer/Panel/VBoxContainer"]
visible = false
layout_mode = 2
offset_left = 145.0
offset_top = -63.0
offset_right = 445.0
offset_bottom = 33.0
theme_override_colors/default_color = Color(1, 1, 1, 1)
theme_override_colors/font_outline_color = Color(1, 0, 1, 0.843137)
theme_override_constants/outline_size = 15
theme_override_font_sizes/normal_font_size = 60
bbcode_enabled = true
text = "恭喜过关"
horizontal_alignment = 1
vertical_alignment = 1

[node name="LevelLabel" type="Label" parent="CenterContainer/Panel/VBoxContainer"]
layout_mode = 2
offset_top = 47.0
offset_right = 600.0
offset_bottom = 93.0
theme_override_colors/font_color = Color(1, 1, 0, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 0.556863)
theme_override_constants/shadow_offset_x = 3
theme_override_constants/shadow_offset_y = 2
theme_override_fonts/font = ExtResource("2_qgr56")
theme_override_font_sizes/font_size = 28
text = "关卡 1 通关！选择一个奖励"
horizontal_alignment = 1

[node name="GourdContainer" type="Control" parent="CenterContainer/Panel/VBoxContainer"]
custom_minimum_size = Vector2(400, 100)
layout_mode = 2
anchors_preset = 0
offset_left = 76.0
offset_top = 108.0
offset_right = 526.0
offset_bottom = 278.0

[node name="ButtonsContainer" type="Control" parent="CenterContainer/Panel/VBoxContainer"]
layout_mode = 2
anchors_preset = 0
offset_left = 68.0
offset_top = 319.0
offset_right = 532.0
offset_bottom = 359.0

[node name="NextLevelButton" type="Button" parent="CenterContainer/Panel/VBoxContainer/ButtonsContainer"]
layout_mode = 2
offset_left = 195.0
offset_right = 259.0
offset_bottom = 31.0
text = "下一关"

[node name="Shadow" type="TextureRect" parent="CenterContainer/Panel/VBoxContainer/ButtonsContainer/NextLevelButton"]
modulate = Color(0, 0, 0, 0.215686)
layout_mode = 0
offset_left = -10.0
offset_top = -7.0
offset_right = 78.0
offset_bottom = 49.0
texture = ExtResource("5_mk84x")
expand_mode = 1
stretch_mode = 5

[node name="TextureRect" type="TextureRect" parent="CenterContainer/Panel/VBoxContainer/ButtonsContainer/NextLevelButton"]
layout_mode = 0
offset_left = -11.0
offset_top = -11.0
offset_right = 77.0
offset_bottom = 45.0
texture = ExtResource("5_mk84x")
expand_mode = 1
stretch_mode = 5

[node name="Mask" type="TextureRect" parent="CenterContainer/Panel/VBoxContainer/ButtonsContainer/NextLevelButton"]
unique_name_in_owner = true
modulate = Color(0, 0, 0, 0.443137)
layout_mode = 0
offset_left = -4.0
offset_top = -5.0
offset_right = 68.0
offset_bottom = 38.0
texture = ExtResource("5_mk84x")
expand_mode = 1
stretch_mode = 5

[node name="MainMenuButton" type="Button" parent="CenterContainer/Panel/VBoxContainer/ButtonsContainer"]
visible = false
custom_minimum_size = Vector2(120, 40)
layout_mode = 2
offset_left = 242.0
offset_right = 362.0
offset_bottom = 40.0
text = "返回主菜单"

[node name="BackGroud" type="Control" parent="."]
visible = false
layout_mode = 3
anchors_preset = 0

[node name="Shadow1" type="TextureRect" parent="BackGroud"]
modulate = Color(0, 0, 0, 0.564706)
layout_mode = 0
offset_left = 665.0
offset_top = 444.0
offset_right = 795.0
offset_bottom = 574.0
pivot_offset = Vector2(90, 85)
texture = ExtResource("5_y3dxj")
expand_mode = 1
stretch_mode = 5
flip_h = true

[node name="Speaker" type="TextureRect" parent="BackGroud"]
modulate = Color(0.813258, 0.813258, 0.813258, 1)
layout_mode = 0
offset_left = 656.0
offset_top = 444.0
offset_right = 786.0
offset_bottom = 574.0
pivot_offset = Vector2(90, 85)
texture = ExtResource("5_y3dxj")
expand_mode = 1
stretch_mode = 5
flip_h = true

[node name="Shadow2" type="TextureRect" parent="BackGroud"]
modulate = Color(0, 0, 0, 0.686275)
layout_mode = 0
offset_left = 360.0
offset_top = 442.0
offset_right = 510.0
offset_bottom = 592.0
pivot_offset = Vector2(85.5, 80)
texture = ExtResource("6_mk84x")
expand_mode = 1
stretch_mode = 5

[node name="Gooder" type="TextureRect" parent="BackGroud"]
modulate = Color(0.813258, 0.813258, 0.813258, 1)
layout_mode = 0
offset_left = 352.0
offset_top = 442.0
offset_right = 502.0
offset_bottom = 592.0
pivot_offset = Vector2(85.5, 80)
texture = ExtResource("6_mk84x")
expand_mode = 1
stretch_mode = 5
