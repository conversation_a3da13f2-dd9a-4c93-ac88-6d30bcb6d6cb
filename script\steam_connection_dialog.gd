extends Control

# Steam连接失败弹框

# 节点引用
@onready var dialog_panel: Panel = $CenterContainer/DialogPanel
@onready var title_label: Label = $CenterContainer/DialogPanel/VBoxContainer/TitleLabel
@onready var message_label: Label = $CenterContainer/DialogPanel/VBoxContainer/MessageLabel
@onready var reconnect_button: Button = $CenterContainer/DialogPanel/VBoxContainer/ButtonContainer/ReconnectButton
@onready var exit_button: Button = $CenterContainer/DialogPanel/VBoxContainer/ButtonContainer/ExitButton
@onready var loading_container: HBoxContainer = $CenterContainer/DialogPanel/VBoxContainer/LoadingContainer
@onready var loading_spinner: Label = $CenterContainer/DialogPanel/VBoxContainer/LoadingContainer/LoadingSpinner
@onready var button_container: HBoxContainer = $CenterContainer/DialogPanel/VBoxContainer/ButtonContainer

# 重连相关变量
var reconnect_attempts: int = 0
var max_reconnect_attempts: int = 3
var is_reconnecting: bool = false
var spinner_tween: Tween

# 信号
signal reconnect_requested
signal exit_requested

func _ready() -> void:
    # 设置初始状态
    visible = false
    modulate.a = 0.0
    dialog_panel.scale = Vector2(0.8, 0.8)
    
    # 连接按钮信号
    reconnect_button.pressed.connect(_on_reconnect_button_pressed)
    exit_button.pressed.connect(_on_exit_button_pressed)
    
    # 设置样式
    _setup_dialog_style()
    
    # 应用UI管理器主题
    var ui_manager = get_node_or_null("/root/UIManager")
    if ui_manager:
        ui_manager.apply_theme_recursive(self, ui_manager.game_theme)

    # 连接语言变化信号
    var language_manager = get_node_or_null("/root/LanguageManager")
    if language_manager:
        language_manager.language_changed.connect(_on_language_changed)

    # 初始化文本
    _update_all_texts()

# 设置弹框样式
func _setup_dialog_style() -> void:
    # 设置面板样式（按照设置面板的样式）
    _setup_panel_style()

    # 设置标题样式
    title_label.add_theme_font_size_override("font_size", 28)
    title_label.add_theme_color_override("font_color", Color(1.0, 0.8, 0.2, 1.0))  # 金色
    title_label.add_theme_constant_override("outline_size", 4)
    title_label.add_theme_color_override("font_outline_color", Color(0.0, 0.0, 0.0, 0.8))

    # 设置消息样式
    message_label.add_theme_font_size_override("font_size", 18)
    message_label.add_theme_color_override("font_color", Color(1.0, 1.0, 1.0, 0.9))

    # 设置按钮样式（使用设置面板的按钮样式）
    _setup_button_style(reconnect_button, Color(0.2, 0.4, 0.8, 1.0))  # 蓝色（与设置面板一致）
    _setup_button_style(exit_button, Color(0.8, 0.2, 0.2, 0.9))      # 红色

    # 设置加载文本样式
    loading_spinner.add_theme_font_size_override("font_size", 16)
    loading_spinner.add_theme_color_override("font_color", Color(0.8, 0.8, 1.0, 1.0))

# 设置面板样式（按照设置面板的样式）
func _setup_panel_style() -> void:
    # 创建面板样式，与设置面板保持一致
    var panel_style = StyleBoxFlat.new()
    panel_style.bg_color = Color(0.1, 0.1, 0.2, 0.95)  # 与设置面板一致的背景色
    panel_style.corner_radius_top_left = 25
    panel_style.corner_radius_top_right = 25
    panel_style.corner_radius_bottom_left = 25
    panel_style.corner_radius_bottom_right = 25
    panel_style.border_width_left = 4
    panel_style.border_width_top = 4
    panel_style.border_width_right = 4
    panel_style.border_width_bottom = 4
    panel_style.border_color = Color(0.3, 0.3, 0.7, 1.0)  # 蓝色边框
    panel_style.shadow_color = Color(0, 0, 0, 0.5)
    panel_style.shadow_size = 15
    panel_style.shadow_offset = Vector2(2, 2)

    # 应用样式到对话框面板
    dialog_panel.add_theme_stylebox_override("panel", panel_style)

    # 调整面板大小
    dialog_panel.custom_minimum_size = Vector2(400, 300)

    # 设置旋转中心点
    dialog_panel.pivot_offset = dialog_panel.size / 2

# 设置按钮样式（按照设置面板的样式）
func _setup_button_style(button: Button, color: Color) -> void:
    # 设置按钮大小
    button.custom_minimum_size = Vector2(170, 50)
    button.pivot_offset = button.custom_minimum_size / 2

    # 创建按钮样式（与设置面板保持一致）
    var normal_style = StyleBoxFlat.new()
    normal_style.bg_color = color
    normal_style.corner_radius_top_left = 15
    normal_style.corner_radius_top_right = 15
    normal_style.corner_radius_bottom_left = 15
    normal_style.corner_radius_bottom_right = 15
    normal_style.border_width_left = 4
    normal_style.border_width_top = 4
    normal_style.border_width_right = 4
    normal_style.border_width_bottom = 4
    normal_style.border_color = Color(0.4, 0.6, 1.0, 1.0)  # 统一的蓝色边框

    # 创建悬停样式
    var hover_style = normal_style.duplicate()
    hover_style.bg_color = Color(0.3, 0.5, 0.9, 1.0)
    hover_style.border_color = Color(0.5, 0.7, 1.0, 1.0)

    # 创建按下样式
    var pressed_style = normal_style.duplicate()
    pressed_style.bg_color = Color(0.1, 0.3, 0.7, 1.0)
    pressed_style.border_color = Color(0.4, 0.6, 1.0, 1.0)
    pressed_style.border_width_bottom = 2  # 按下时底部边框变细

    # 应用样式
    button.add_theme_stylebox_override("normal", normal_style)
    button.add_theme_stylebox_override("hover", hover_style)
    button.add_theme_stylebox_override("pressed", pressed_style)

    # 字体样式
    button.add_theme_font_size_override("font_size", 18)
    button.add_theme_color_override("font_color", Color(1.0, 1.0, 1.0, 1.0))
    button.add_theme_constant_override("outline_size", 2)
    button.add_theme_color_override("font_outline_color", Color(0.0, 0.0, 0.0, 0.5))

# 显示弹框
func show_dialog() -> void:
    # 重置状态
    reconnect_attempts = 0
    is_reconnecting = false
    _update_dialog_content()
    
    # 显示弹框
    visible = true
    
    # 播放弹出动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_BACK)
    
    tween.parallel().tween_property(self, "modulate:a", 1.0, 0.3)
    tween.parallel().tween_property(dialog_panel, "scale", Vector2(1.0, 1.0), 0.3)

# 隐藏弹框
func hide_dialog() -> void:
    # 停止加载动画
    _stop_loading_animation()
    
    # 播放淡出动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_IN)
    tween.set_trans(Tween.TRANS_QUART)
    
    tween.parallel().tween_property(self, "modulate:a", 0.0, 0.2)
    tween.parallel().tween_property(dialog_panel, "scale", Vector2(0.8, 0.8), 0.2)
    
    await tween.finished
    visible = false

# 更新弹框内容
func _update_dialog_content() -> void:
    if reconnect_attempts >= max_reconnect_attempts:
        # 达到最大重连次数
        title_label.text = tr("STEAM_CONNECTION_FAILED")
        message_label.text = tr("STEAM_CONNECTION_FAILED_CHECK_NETWORK")
        reconnect_button.visible = false
        button_container.alignment = BoxContainer.ALIGNMENT_CENTER
    else:
        # 还可以重连
        title_label.text = tr("STEAM_CONNECTION_FAILED")
        message_label.text = tr("STEAM_NOT_CONNECTED_MESSAGE")
        reconnect_button.visible = true
        button_container.alignment = BoxContainer.ALIGNMENT_CENTER

    # 更新按钮文本
    _update_button_texts()

# 重新连接按钮点击
func _on_reconnect_button_pressed() -> void:
    if is_reconnecting:
        return

    reconnect_attempts += 1
    is_reconnecting = true

    # 显示加载状态
    _show_loading_state()

    # 等待3秒后再发送重连信号（让用户看到等待效果）
    await get_tree().create_timer(3.0).timeout

    # 发送重连信号
    emit_signal("reconnect_requested")

# 退出按钮点击
func _on_exit_button_pressed() -> void:
    emit_signal("exit_requested")

# 显示加载状态
func _show_loading_state() -> void:
    button_container.visible = false
    loading_container.visible = true
    
    # 开始旋转动画
    _start_loading_animation()

# 隐藏加载状态
func _hide_loading_state() -> void:
    loading_container.visible = false
    button_container.visible = true
    
    # 停止旋转动画
    _stop_loading_animation()

# 开始加载动画
func _start_loading_animation() -> void:
    if spinner_tween:
        spinner_tween.kill()
    
    spinner_tween = create_tween()
    spinner_tween.set_loops()
    
    # 文本闪烁效果
    spinner_tween.tween_property(loading_spinner, "modulate:a", 0.3, 0.5)
    spinner_tween.tween_property(loading_spinner, "modulate:a", 1.0, 0.5)

# 停止加载动画
func _stop_loading_animation() -> void:
    if spinner_tween:
        spinner_tween.kill()
        spinner_tween = null
    loading_spinner.modulate.a = 1.0

# 重连成功
func on_reconnect_success() -> void:
    is_reconnecting = false
    hide_dialog()

# 重连失败
func on_reconnect_failed() -> void:
    is_reconnecting = false
    _hide_loading_state()
    _update_dialog_content()

# 语言变化处理
func _on_language_changed() -> void:
    _update_all_texts()

# 更新所有文本
func _update_all_texts() -> void:
    _update_dialog_content()
    _update_button_texts()
    _update_loading_text()

# 更新按钮文本
func _update_button_texts() -> void:
    reconnect_button.text = tr("STEAM_RECONNECT")
    exit_button.text = tr("EXIT_GAME")

# 更新加载文本
func _update_loading_text() -> void:
    loading_spinner.text = tr("STEAM_CONNECTING")
