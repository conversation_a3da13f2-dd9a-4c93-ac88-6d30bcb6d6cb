[gd_scene load_steps=16 format=3 uid="uid://c8q6y7j5n4g8t"]

[ext_resource type="Shader" uid="uid://bawkvu87t8s5n" path="res://shader/dice_panel_shader.gdshader" id="1_aajbx"]
[ext_resource type="Script" uid="uid://cuuiua557e5ki" path="res://script/dice_panel.gd" id="1_d1c3p"]
[ext_resource type="Texture2D" uid="uid://yln6dam8g3ee" path="res://assert/dice_panel/dice_back02.png" id="3_32ofu"]
[ext_resource type="StyleBox" uid="uid://bqnv6cucb8e66" path="res://themes/dice_panel_style.tres" id="3_style"]
[ext_resource type="Texture2D" uid="uid://4urjxxnqo5cy" path="res://assert/items/duck_hi.png" id="3_t0f64"]
[ext_resource type="Texture2D" uid="uid://o6jv6n1xyrin" path="res://assert/dice_panel/connect1.png" id="4_aajbx"]
[ext_resource type="Texture2D" uid="uid://cnjn06lwg33qf" path="res://assert/coins/cash.png" id="4_cash"]
[ext_resource type="Texture2D" uid="uid://b27xs2f1s5gjl" path="res://assert/dice_panel/dice_back05.png" id="4_gyp0k"]
[ext_resource type="Texture2D" uid="uid://feuuhffpedxx" path="res://assert/dict/UpscaledDice_Pink.png" id="4_rwu52"]

[sub_resource type="Gradient" id="Gradient_32ofu"]
colors = PackedColorArray(0, 0, 0, 1, 1, 0, 1, 1)

[sub_resource type="GradientTexture2D" id="GradientTexture2D_t0f64"]
gradient = SubResource("Gradient_32ofu")
fill = 1
fill_from = Vector2(0.5, 0.5)

[sub_resource type="ShaderMaterial" id="ShaderMaterial_32ofu"]
shader = ExtResource("1_aajbx")
shader_parameter/gradient = SubResource("GradientTexture2D_t0f64")
shader_parameter/spread = 0.59
shader_parameter/cutoff = 2.0
shader_parameter/size = 0.935
shader_parameter/speed = 1.0
shader_parameter/ray1_density = 10.0
shader_parameter/ray2_density = 20.0
shader_parameter/ray2_intensity = 0.8
shader_parameter/core_intensity = 2.0
shader_parameter/hdr = false
shader_parameter/seed = 6.0

[sub_resource type="Animation" id="Animation_t0f64"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("AnimationPlayer/Sprite2D:frame_coords")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [Vector2i(0, 5)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("AnimationPlayer/Sprite2D:rotation")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("AnimationPlayer/Sprite2D:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(460, 260)]
}

[sub_resource type="Animation" id="Animation_yx8j6"]
resource_name = "roll"
length = 0.5
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("AnimationPlayer/Sprite2D:frame_coords")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3, 0.4, 0.5),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1),
"update": 1,
"values": [Vector2i(0, 5), Vector2i(1, 5), Vector2i(2, 5), Vector2i(3, 5), Vector2i(4, 5), Vector2i(5, 5)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("AnimationPlayer/Sprite2D:rotation")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3, 0.4, 0.5),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1),
"update": 1,
"values": [0.0, 1.0472, 2.0944, 3.14159, 4.18879, 5.23599]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("AnimationPlayer/Sprite2D:position")
tracks/2/interp = 2
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3, 0.5),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [Vector2(460, 300), Vector2(460, 310), Vector2(460, 310), Vector2(460, 300)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_32ofu"]
_data = {
&"RESET": SubResource("Animation_t0f64"),
&"roll": SubResource("Animation_yx8j6")
}

[node name="DicePanel" type="Panel"]
z_index = 60
custom_minimum_size = Vector2(500, 300)
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -250.0
offset_top = -150.0
offset_right = 250.0
offset_bottom = 150.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_styles/panel = ExtResource("3_style")
script = ExtResource("1_d1c3p")

[node name="Shadow" type="TextureRect" parent="."]
modulate = Color(0, 0, 0, 0.258824)
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -64.0
offset_top = -17.0
offset_right = 76.0
offset_bottom = 67.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("3_32ofu")
expand_mode = 1
stretch_mode = 5

[node name="TextureRect" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -70.0
offset_top = -46.0
offset_right = 70.0
offset_bottom = 38.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("4_gyp0k")
expand_mode = 1
stretch_mode = 5

[node name="Connect" type="Control" parent="."]
visible = false
anchors_preset = 0
offset_right = 40.0
offset_bottom = 40.0

[node name="Connect1" type="TextureRect" parent="Connect"]
layout_mode = 0
offset_left = 146.0
offset_top = -177.0
offset_right = 194.0
offset_bottom = 17.0
texture = ExtResource("4_aajbx")
expand_mode = 1
stretch_mode = 5

[node name="BackGround" type="Control" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="Shadow" type="TextureRect" parent="BackGround"]
visible = false
modulate = Color(0, 0, 0, 0.447059)
layout_mode = 0
offset_left = 328.0
offset_top = 135.0
offset_right = 536.0
offset_bottom = 345.0
scale = Vector2(0.5, 0.5)
pivot_offset = Vector2(100, 100)
texture = ExtResource("3_t0f64")
expand_mode = 1
stretch_mode = 5

[node name="TextureRect" type="TextureRect" parent="BackGround"]
visible = false
layout_mode = 0
offset_left = 318.0
offset_top = 136.0
offset_right = 526.0
offset_bottom = 346.0
scale = Vector2(0.5, 0.5)
pivot_offset = Vector2(100, 100)
texture = ExtResource("3_t0f64")
expand_mode = 1
stretch_mode = 5

[node name="GameOverlay" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -326.0
offset_top = -20.0
offset_right = 326.0
offset_bottom = 175.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(578, 317)
color = Color(0, 0, 0, 0)

[node name="Shine" type="ColorRect" parent="."]
show_behind_parent = true
material = SubResource("ShaderMaterial_32ofu")
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -518.0
offset_top = -366.0
offset_right = 518.0
offset_bottom = 358.0
grow_horizontal = 2
grow_vertical = 2
pivot_offset = Vector2(578, 317)
mouse_filter = 2

[node name="TopInfo" type="HBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 10
anchor_right = 1.0
offset_left = 138.0
offset_top = 17.0
offset_right = -132.0
offset_bottom = 57.0
grow_horizontal = 2

[node name="DiceIconsContainer" type="HBoxContainer" parent="TopInfo"]
custom_minimum_size = Vector2(160, 0)
layout_mode = 2
alignment = 1

[node name="FixedLimitLabel" type="Label" parent="TopInfo"]
visible = false
layout_mode = 2
size_flags_horizontal = 3
text = "固定限制积分: 30"
horizontal_alignment = 1

[node name="DiceContainer" type="HBoxContainer" parent="TopInfo"]
custom_minimum_size = Vector2(70, 0)
layout_mode = 2
alignment = 1

[node name="DiceSumTitle" type="RichTextLabel" parent="TopInfo/DiceContainer"]
layout_mode = 2
size_flags_horizontal = 3
bbcode_enabled = true
text = "[img=32]res://assert/top/ap.png[/img]"
vertical_alignment = 1

[node name="DiceSumLabel" type="Label" parent="TopInfo/DiceContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_colors/font_outline_color = Color(0, 0, 0, 1)
theme_override_constants/outline_size = 4
text = " --"
vertical_alignment = 1

[node name="ScoreContainer" type="HBoxContainer" parent="TopInfo"]
visible = false
layout_mode = 2
size_flags_horizontal = 3
alignment = 1

[node name="ScoreIcon" type="TextureRect" parent="TopInfo/ScoreContainer"]
visible = false
custom_minimum_size = Vector2(30, 30)
layout_mode = 2
size_flags_vertical = 4
texture = ExtResource("4_cash")
expand_mode = 1
stretch_mode = 5

[node name="LimitScoreTitle" type="Label" parent="TopInfo/ScoreContainer"]
visible = false
layout_mode = 2
text = "阶目标："
vertical_alignment = 1

[node name="LimitScoreLabel" type="Label" parent="TopInfo/ScoreContainer"]
visible = false
layout_mode = 2
text = "--"
vertical_alignment = 1

[node name="CenterContainer" type="CenterContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 18.0
offset_top = 74.0
offset_right = -18.0
offset_bottom = -74.0
grow_horizontal = 2
grow_vertical = 2

[node name="DiceContainer" type="Control" parent="CenterContainer"]
custom_minimum_size = Vector2(460, 152)
layout_mode = 2

[node name="Dice1" type="TextureRect" parent="CenterContainer/DiceContainer"]
custom_minimum_size = Vector2(80, 80)
layout_mode = 2
offset_right = 80.0
offset_bottom = 80.0
expand_mode = 1

[node name="Dice2" type="TextureRect" parent="CenterContainer/DiceContainer"]
custom_minimum_size = Vector2(80, 80)
layout_mode = 2
offset_left = 100.0
offset_right = 180.0
offset_bottom = 80.0
expand_mode = 1

[node name="Dice3" type="TextureRect" parent="CenterContainer/DiceContainer"]
custom_minimum_size = Vector2(80, 80)
layout_mode = 2
offset_left = 200.0
offset_right = 280.0
offset_bottom = 80.0
expand_mode = 1

[node name="Dice4" type="TextureRect" parent="CenterContainer/DiceContainer"]
custom_minimum_size = Vector2(80, 80)
layout_mode = 2
offset_left = 300.0
offset_right = 380.0
offset_bottom = 80.0
expand_mode = 1

[node name="Dice5" type="TextureRect" parent="CenterContainer/DiceContainer"]
custom_minimum_size = Vector2(80, 80)
layout_mode = 2
offset_left = 400.0
offset_right = 480.0
offset_bottom = 80.0
expand_mode = 1

[node name="Dice6" type="TextureRect" parent="CenterContainer/DiceContainer"]
custom_minimum_size = Vector2(80, 80)
layout_mode = 2
offset_left = -10.0
offset_top = 72.0
offset_right = 70.0
offset_bottom = 152.0
expand_mode = 1

[node name="Dice7" type="TextureRect" parent="CenterContainer/DiceContainer"]
custom_minimum_size = Vector2(80, 80)
layout_mode = 2
offset_left = 90.0
offset_top = 72.0
offset_right = 170.0
offset_bottom = 152.0
expand_mode = 1

[node name="Dice8" type="TextureRect" parent="CenterContainer/DiceContainer"]
custom_minimum_size = Vector2(80, 80)
layout_mode = 2
offset_left = 190.0
offset_top = 72.0
offset_right = 270.0
offset_bottom = 152.0
expand_mode = 1

[node name="Dice9" type="TextureRect" parent="CenterContainer/DiceContainer"]
custom_minimum_size = Vector2(80, 80)
layout_mode = 2
offset_left = 290.0
offset_top = 72.0
offset_right = 370.0
offset_bottom = 152.0
expand_mode = 1

[node name="Dice10" type="TextureRect" parent="CenterContainer/DiceContainer"]
custom_minimum_size = Vector2(80, 80)
layout_mode = 2
offset_left = 390.0
offset_top = 72.0
offset_right = 470.0
offset_bottom = 152.0
expand_mode = 1

[node name="AnimationPlayer" type="AnimationPlayer" parent="CenterContainer/DiceContainer"]
libraries = {
&"": SubResource("AnimationLibrary_32ofu")
}

[node name="Sprite2D" type="Sprite2D" parent="CenterContainer/DiceContainer/AnimationPlayer"]
visible = false
position = Vector2(460, 260)
scale = Vector2(0.5, 0.5)
texture = ExtResource("4_rwu52")
hframes = 6
vframes = 6
frame = 30

[node name="BottomButtons" type="Control" parent="."]
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 106.0
offset_top = -77.0
offset_right = -110.0
offset_bottom = -37.0
grow_horizontal = 2
grow_vertical = 0

[node name="RollButton" type="Button" parent="BottomButtons"]
layout_mode = 2
offset_left = 46.0
offset_top = -1.0
offset_right = 85.0
offset_bottom = 39.0

[node name="Spacer" type="Control" parent="BottomButtons"]
custom_minimum_size = Vector2(50, 0)
layout_mode = 2
anchors_preset = 0
offset_left = 208.0
offset_right = 258.0
offset_bottom = 40.0

[node name="ConfirmButton" type="Button" parent="BottomButtons"]
layout_mode = 2
offset_left = 185.0
offset_right = 223.0
offset_bottom = 40.0
