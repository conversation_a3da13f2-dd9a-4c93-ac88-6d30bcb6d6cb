extends Control

# 卡牌收藏展示页面 - 显示所有奖励卡牌的收藏状态

# 节点引用
@onready var card_container = $MainContainer/CardDisplayArea/CardScrollContainer/CardContainer
@onready var return_button = $MainContainer/BottomContainer/ReturnButton
# 移除旧的tooltip引用，改用固定信息区域
@onready var zoo_tab_button = $MainContainer/CategoryTabContainer/TabButtonContainer/ZooTabButton
@onready var rewards_tab_button = $MainContainer/CategoryTabContainer/TabButtonContainer/RewardsTabButton
@onready var card_display_area = $MainContainer/CardDisplayArea
@onready var card_info_name_label = $MainContainer/CardInfoArea/CardInfoContainer/CardInfoPanel/CardInfoVContainer/CardInfoNameLabel
@onready var card_info_desc_label = $MainContainer/CardInfoArea/CardInfoContainer/CardInfoPanel/CardInfoVContainer/CardInfoDescLabel
@onready var left_scroll_button = $MainContainer/CardInfoArea/CardInfoContainer/LeftScrollButton
@onready var right_scroll_button = $MainContainer/CardInfoArea/CardInfoContainer/RightScrollButton

# 卡牌相关变量
var card_nodes = []
var zoo_card_data_list = []  # zoo_buff 卡牌数据
var reward_card_data_list = []  # buff_rewards 卡牌数据
var all_card_data_list = []  # 所有卡牌数据
var scroll_offset = 0.0
var target_scroll_offset = 0.0
var card_spacing = 200.0
var center_position = 0.0
var is_dragging = false
var drag_start_pos = Vector2.ZERO
var drag_start_offset = 0.0
var last_interaction_time = 0.0  # 最后一次交互的时间

# 动画控制变量
var is_playing_fly_in_animation = false

# 音效控制变量
var last_center_card_index = -1  # 上一次的中心卡牌索引，用于检测卡牌切换
var last_scroll_sfx_time = 0.0  # 上一次播放滚动音效的时间
var scroll_sfx_cooldown = 0.1  # 滚动音效冷却时间（秒）

# 按钮控制相关变量（简化版，仅支持单次点击）

# 心跳效果相关变量已移除，现在使用shader内置的名称显示

# 卡牌尺寸和缩放
var base_card_size = Vector2(150, 210)
var max_scale = 1.2
var min_scale = 0.6
var scale_distance = 300.0

# 分类相关变量
var current_category = "zoo"  # "zoo" 或 "rewards"
var zoo_start_index = 0
var rewards_start_index = 0

# 切换栏节点引用
var category_tabs = null

# 初始化
func _ready():
    # 播放卡牌收藏页面的背景音乐
    _setup_collection_music()

    # 连接返回按钮信号
    return_button.pressed.connect(_on_return_button_pressed)

    # 连接分类切换按钮
    zoo_tab_button.pressed.connect(_on_zoo_tab_pressed)
    rewards_tab_button.pressed.connect(_on_rewards_tab_pressed)

    # 连接滚动按钮信号（仅使用pressed信号，单次点击）
    left_scroll_button.pressed.connect(_on_left_button_pressed)
    right_scroll_button.pressed.connect(_on_right_button_pressed)

    # 为左右滚动按钮添加自定义音效标记，跳过UIManager的默认音效
    left_scroll_button.set_meta("custom_sfx", true)
    right_scroll_button.set_meta("custom_sfx", true)

    # 设置按钮文字
    _setup_button_texts()

    # 设置标签页按钮样式
    _setup_tab_button_styles()

    # 加载卡牌数据
    _load_card_data()

    # 创建卡牌节点
    _create_card_nodes()

    # 设置初始滚动位置（显示第一张卡牌在中心）
    scroll_offset = 0.0
    target_scroll_offset = 0.0

    # 延迟初始化位置，确保视口大小已经正确设置
    call_deferred("_initialize_positions")

    # 应用UI管理器主题
    UIManager.init_scene_ui(self)

    # 启用鼠标移动事件监听
    set_process_input(true)

# 设置卡牌收藏页面的背景音乐
func _setup_collection_music():
    var audio_manager = get_node_or_null("/root/AudioManager")
    if audio_manager:
        # 播放卡牌收藏页面的背景音乐
        audio_manager.play_music("res://assert/audio/music/bgm006.mp3")
        # print("卡牌收藏页面：切换到背景音乐 bgm006.mp3")
    else:
        print("警告：无法获取AudioManager，无法播放背景音乐")

# 设置按钮文字
func _setup_button_texts():
    # 设置标签页按钮的本地化文字
    zoo_tab_button.text = tr("ZooTabButton")
    rewards_tab_button.text = tr("RewardsTabButton")

    # 设置其他按钮的文字
    return_button.text = tr("RETURN")

    # 设置标题文字
    var title_label = $MainContainer/TitleContainer/CardCollTitleLabel
    if title_label:
        title_label.text = tr("CardCollTitleLabel")

# 设置标签页按钮样式
func _setup_tab_button_styles():
    # 加载按钮背景图片
    var button_texture = load("res://assert/collection/button1.png")
    if not button_texture:
        print("警告：无法加载按钮背景图片")
        return

    # 为两个按钮设置样式（只在未初始化时设置）
    if not zoo_tab_button.has_meta("style_initialized"):
        _setup_single_tab_button_style(zoo_tab_button, button_texture)
    if not rewards_tab_button.has_meta("style_initialized"):
        _setup_single_tab_button_style(rewards_tab_button, button_texture)

    # 连接鼠标悬停事件
    _setup_tab_button_hover_effects()

    # 初始化按钮状态
    _update_tab_buttons()

# 设置单个标签页按钮的样式
func _setup_single_tab_button_style(button: Button, texture: Texture2D):
    # 创建基础样式（只设置一次）
    var base_style = StyleBoxTexture.new()
    base_style.texture = texture
    base_style.modulate_color = Color.WHITE  # 使用白色作为基础，通过按钮modulate控制颜色

    # 应用基础样式到所有状态（只设置一次，避免重复创建）
    button.add_theme_stylebox_override("normal", base_style)
    button.add_theme_stylebox_override("pressed", base_style)
    button.add_theme_stylebox_override("hover", base_style)
    button.add_theme_stylebox_override("focus", base_style)

    # 设置文字颜色为白色以确保可见性
    button.add_theme_color_override("font_color", Color.WHITE)
    button.add_theme_color_override("font_pressed_color", Color.WHITE)
    button.add_theme_color_override("font_hover_color", Color.WHITE)

    # 标记已设置样式，避免重复设置
    button.set_meta("style_initialized", true)
    # 标记为自定义按钮，防止UIManager的通用处理
    button.set_meta("custom_button", true)

    # 设置动态字体大小
    _setup_dynamic_font_size(button)

# 设置按钮的动态字体大小
func _setup_dynamic_font_size(button: Button):
    if not button:
        return

    # 获取按钮文字
    var text = button.text
    if text.is_empty():
        return

    # 按钮的可用宽度（减去内边距）
    var button_width = button.custom_minimum_size.x
    var button_height = button.custom_minimum_size.y

    # 如果没有设置custom_minimum_size，使用默认尺寸
    if button_width <= 0:
        button_width = 120  # 默认宽度
    if button_height <= 0:
        button_height = 40  # 默认高度

    # 设置内边距（左右各留10像素，上下各留5像素的安全距离）
    var horizontal_padding = 20  # 左右总共20像素
    var vertical_padding = 10    # 上下总共10像素

    var available_width = button_width - horizontal_padding
    var available_height = button_height - vertical_padding

    # 计算合适的字体大小
    var optimal_font_size = _calculate_optimal_font_size(text, available_width, available_height)

    # 应用字体大小
    button.add_theme_font_size_override("font_size", optimal_font_size)

    print("按钮 '", text, "' 设置字体大小: ", optimal_font_size)

# 计算最佳字体大小
func _calculate_optimal_font_size(text: String, max_width: float, max_height: float) -> int:
    # 字体大小范围
    var min_font_size = 10
    var max_font_size = 20
    var optimal_size = max_font_size

    # 创建临时字体来测量文字尺寸
    var temp_font = ThemeDB.fallback_font
    if not temp_font:
        # 尝试加载思源字体
        var font_path = "res://fonts/siyuan.ttf"
        if ResourceLoader.exists(font_path):
            temp_font = ResourceLoader.load(font_path) as FontFile

        if not temp_font:
            # 如果还是没有字体，返回中等大小
            print("警告：无法获取字体，使用默认字体大小")
            return 16

    # 从最大字体开始，逐步减小直到文字能完全显示
    for font_size in range(max_font_size, min_font_size - 1, -1):
        var text_size = temp_font.get_string_size(text, HORIZONTAL_ALIGNMENT_LEFT, -1, font_size)

        # 检查文字是否能在可用空间内显示
        if text_size.x <= max_width and text_size.y <= max_height:
            optimal_size = font_size
            break

    # 确保字体大小在合理范围内
    var final_size = max(min_font_size, min(max_font_size, optimal_size))

    # 添加调试信息
    print("文字 '", text, "' 在尺寸 ", max_width, "x", max_height, " 中的最佳字体大小: ", final_size)

    return final_size

# 更新按钮文字并调整字体大小
func _update_button_text_and_font(button: Button, new_text: String):
    if not button:
        return

    # 设置新文字
    button.text = new_text

    # 重新计算字体大小
    _setup_dynamic_font_size(button)

# 刷新所有按钮的字体大小（用于语言切换后）
func _refresh_all_button_fonts():
    if zoo_tab_button and zoo_tab_button.has_meta("style_initialized"):
        _setup_dynamic_font_size(zoo_tab_button)
    if rewards_tab_button and rewards_tab_button.has_meta("style_initialized"):
        _setup_dynamic_font_size(rewards_tab_button)

# 延迟初始化位置
func _initialize_positions():
    # 获取CardDisplayArea的中心位置
    center_position = card_display_area.size.x / 2.0

    # 初始化中心卡牌索引（避免在初始化时播放音效）
    last_center_card_index = _get_current_center_card_index()

    # 启动卡牌飞入动画
    _start_cards_fly_in_animation()

    # 初始化卡牌信息显示（延迟到动画完成后）
    await get_tree().create_timer(1.0).timeout
    _update_center_card_info()

# 启动卡牌飞入动画
func _start_cards_fly_in_animation():
    if card_nodes.is_empty():
        return

    # 设置动画标志
    is_playing_fly_in_animation = true

    # 计算每张卡牌的最终位置、缩放和旋转
    var final_positions = []
    var final_scales = []
    var final_rotations = []
    var final_y_positions = []

    for i in range(card_nodes.size()):
        # 计算目标位置（与_update_card_positions中的逻辑一致）
        var target_x = center_position + (i * card_spacing) + scroll_offset
        var target_y = card_display_area.size.y / 2.0 - base_card_size.y / 2.0

        # 计算距离中心的距离
        var distance_from_center = abs(target_x - center_position)

        # 计算缩放比例（与_update_card_positions中的逻辑一致）
        var scale_factor = max(min_scale, max_scale - (distance_from_center / scale_distance) * (max_scale - min_scale))
        scale_factor = max(min_scale, scale_factor)

        # 计算旋转角度（与_update_card_positions中的逻辑一致）
        var rotation_angle = _calculate_card_rotation(i, distance_from_center)

        # 计算缩放后的位置偏移
        var scaled_size = base_card_size * scale_factor
        var scale_offset_y = (scaled_size.y - base_card_size.y) / 2.0

        # 最终位置
        var final_x = target_x - scaled_size.x / 2.0
        var final_y = target_y - scale_offset_y

        final_positions.append(Vector2(final_x, final_y))
        final_scales.append(scale_factor)
        final_rotations.append(rotation_angle)
        final_y_positions.append(final_y)

    # 设置所有卡牌的初始位置（屏幕左侧外）
    var start_x = -card_display_area.size.x - 200  # 屏幕左侧外200像素
    for i in range(card_nodes.size()):
        var card_node = card_nodes[i]
        card_node.position.x = start_x
        card_node.position.y = final_y_positions[i]  # 使用计算好的Y位置
        card_node.scale = Vector2(0.7, 0.7)  # 统一的初始缩放

    # 按相反顺序播放飞入动画（最后的卡牌最先飞入）
    # 动态计算时间，确保总时间不超过2秒
    var max_total_time = 1.8  # 最大总时间1.8秒，留0.2秒缓冲
    var single_card_duration = 0.6  # 单张卡牌动画时间
    var delay_increment = min(0.05, (max_total_time - single_card_duration) / max(1, card_nodes.size() - 1))
    var total_animation_time = (card_nodes.size() - 1) * delay_increment + single_card_duration

    # 输出动画时间信息
    print("卡牌飞入动画配置:")
    print("- 卡牌数量: ", card_nodes.size())
    print("- 单张动画时间: ", single_card_duration, "秒")
    print("- 卡牌间隔: ", delay_increment, "秒")
    print("- 总动画时间: ", total_animation_time, "秒")

    # 启动所有卡牌的动画，使用更小的延迟让它们几乎同时开始
    for i in range(card_nodes.size() - 1, -1, -1):  # 从最后一张开始
        var card_node = card_nodes[i]
        var final_position = final_positions[i]
        var final_scale = final_scales[i]
        var final_rotation = final_rotations[i]
        var animation_delay = (card_nodes.size() - 1 - i) * delay_increment

        # 使用call_deferred来避免阻塞，让所有动画几乎同时启动
        _start_single_card_animation.call_deferred(card_node, final_position, final_scale, final_rotation, animation_delay, single_card_duration)

    # 等待所有动画完成后清除标志
    await get_tree().create_timer(total_animation_time).timeout
    is_playing_fly_in_animation = false

# 启动单张卡牌动画（带延迟）
func _start_single_card_animation(card_node: Control, target_position: Vector2, target_scale: float, target_rotation: float, delay: float, duration: float):
    if not card_node:
        return

    # 如果有延迟，等待指定时间
    if delay > 0:
        await get_tree().create_timer(delay).timeout

    # 启动飞入动画
    _animate_card_fly_in(card_node, target_position, target_scale, target_rotation, duration)

# 单张卡牌的飞入动画
func _animate_card_fly_in(card_node: Control, target_position: Vector2, target_scale: float, target_rotation: float, duration: float = 0.8):
    if not card_node:
        return

    # 播放飞入音效（可选）
    # AudioManager.play_sound("card_fly_in")

    # 创建动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_QUART)  # 使用QUART过渡，更快更流畅

    # 设置初始状态（已经在_start_cards_fly_in_animation中设置了初始缩放）
    card_node.modulate.a = 0.5  # 开始时更透明

    # 主要动画：位置
    tween.parallel().tween_property(card_node, "position", target_position, duration)
    # 缩放动画：稍微快一点完成，缩放到目标大小
    tween.parallel().tween_property(card_node, "scale", Vector2(target_scale, target_scale), duration * 0.8)
    # 淡入效果：最快完成
    tween.parallel().tween_property(card_node, "modulate:a", 1.0, duration * 0.5)

    # 旋转动画：从轻微倾斜到目标旋转角度
    card_node.rotation = -0.05  # 开始时稍微倾斜，角度更小
    tween.parallel().tween_property(card_node, "rotation", target_rotation, duration * 0.7)

    # 动画完成时的回调
    tween.finished.connect(_on_card_fly_in_finished.bind(card_node, target_scale, target_rotation))

# 卡牌飞入动画完成回调
func _on_card_fly_in_finished(card_node: Control, target_scale: float, target_rotation: float):
    if not card_node:
        return

    # 确保卡牌的最终状态正确
    card_node.modulate.a = 1.0
    card_node.scale = Vector2(target_scale, target_scale)
    card_node.rotation = target_rotation

    # 可以在这里添加额外的效果，比如轻微的闪光
    # _add_card_arrival_effect(card_node)

# 加载卡牌数据
func _load_card_data():
    var buff_manager = get_node("/root/BuffManager")
    if not buff_manager:
        print("错误：无法获取BuffManager，使用测试数据")
        _load_test_data()
        return

    # 清空所有数据列表
    zoo_card_data_list.clear()
    reward_card_data_list.clear()
    all_card_data_list.clear()

    # 加载 zoo_buff 数据
    _load_zoo_buff_data(buff_manager)

    # 加载 buff_rewards 数据
    _load_buff_rewards_data()

    # 合并所有卡牌数据
    _combine_card_data()

# 加载 zoo_buff 数据
func _load_zoo_buff_data(buff_manager):
    print("BuffManager zoo_buff_pool 大小: ", buff_manager.zoo_buff_pool.size())

    if buff_manager.zoo_buff_pool.size() == 0:
        print("BuffManager zoo_buff_pool 为空")
        return

    for i in range(buff_manager.zoo_buff_pool.size()):
        var buff_data = buff_manager.zoo_buff_pool[i]
        print("处理第 ", i, " 个zoo_buff: ", buff_data.type, " 解锁状态: ", buff_data.is_enabled)

        var card_info = {
            "category": "zoo",
            "type": buff_data.type,
            "name": _get_localized_value(buff_data.name),
            "display_name": _get_localized_value(buff_data.display_name),
            "description": _replace_placeholders_with_question_marks(_get_localized_value(buff_data.explanation_template)),
            "texture_path": BuffManager.get_buff_texture_path(buff_data.type),
            "is_unlocked": buff_data.is_enabled,
            "is_new": _is_card_new("zoo", buff_data.type)
        }
        zoo_card_data_list.append(card_info)

    # 排序：新解锁的在最前面，然后是已解锁的，最后是未解锁的
    zoo_card_data_list.sort_custom(_sort_cards_by_new_and_unlock_status)

    print("加载了 ", zoo_card_data_list.size(), " 张zoo奖励卡牌")

# 加载 buff_rewards 数据
func _load_buff_rewards_data():
    # 从BuffManager获取统一的奖励数据
    var buff_manager = get_node_or_null("/root/BuffManager")
    if buff_manager and buff_manager.has_method("get_rewards_data"):
        var rewards_data = buff_manager.get_rewards_data()
        if rewards_data and not rewards_data.is_empty():
            print("从BuffManager获取buff_rewards数据，共 ", rewards_data.size(), " 个奖励")
            _load_from_rewards_data(rewards_data)
            return

    # 如果无法从BuffManager获取数据，回退到直接加载配置文件
    print("无法从BuffManager获取数据，直接加载配置文件")
    _load_buff_rewards_from_config()

# 从奖励数据加载buff_rewards（通用方法）
func _load_from_rewards_data(rewards_data: Dictionary):
    # 奖励卡牌不需要检查启用状态，只要在胜利页面展示过就算解锁
    # 加载已展示过的奖励记录
    var shown_rewards = _load_shown_rewards()

    # 使用Set避免重复的reward_type
    var processed_types = {}

    for reward_key in rewards_data.keys():
        var reward_data = rewards_data[reward_key]
        # 跳过zoo_buff类型的奖励
        if reward_data.get("is_zoo_buff", false):
            continue

        var reward_type = reward_data.get("type", "")
        if reward_type != "" and not processed_types.has(reward_type):
            processed_types[reward_type] = true  # 标记为已处理

            var card_info = {
                "category": "rewards",
                "type": reward_type,
                "name": reward_data.get("name", "未知奖励"),
                "display_name": reward_data.get("name", "未知奖励"),
                "description": reward_data.get("description", "没有描述"),
                "texture_path": reward_data.get("card_pic", "res://assert/victory_panel/victory.png"),
                "is_unlocked": shown_rewards.has(reward_type),  # 只要展示过就解锁
                "is_new": _is_card_new("rewards", reward_type)
            }
            reward_card_data_list.append(card_info)

    # 排序：新解锁的在最前面，然后是已解锁的，最后是未解锁的
    reward_card_data_list.sort_custom(_sort_cards_by_new_and_unlock_status)

    print("从BuffManager加载了 ", reward_card_data_list.size(), " 张buff_rewards卡牌")

# 从配置文件直接加载buff_rewards（回退方案）
func _load_buff_rewards_from_config():
    var config = ConfigFile.new()
    var err = config.load("res://config/buff_rewards.cfg")

    if err != OK:
        print("无法加载buff_rewards配置文件: ", err)
        return

    # 加载已展示过的奖励记录
    var shown_rewards = _load_shown_rewards()

    var sections = config.get_sections()
    for section in sections:
        var reward_type = config.get_value(section, "type", "")
        if reward_type != "":
            var card_info = {
                "category": "rewards",
                "type": reward_type,
                "name": _get_localized_value(config.get_value(section, "name", {})),
                "display_name": _get_localized_value(config.get_value(section, "name", {})),
                "description": _get_localized_value(config.get_value(section, "description", {})),
                "texture_path": config.get_value(section, "card_pic", "res://assert/victory_panel/victory.png"),
                "is_unlocked": shown_rewards.has(reward_type),  # 只要展示过就解锁
                "is_new": _is_card_new("rewards", reward_type)
            }
            reward_card_data_list.append(card_info)

    # 排序：新解锁的在最前面，然后是已解锁的，最后是未解锁的
    reward_card_data_list.sort_custom(_sort_cards_by_new_and_unlock_status)

    print("加载了 ", reward_card_data_list.size(), " 张buff_rewards卡牌")

# 加载已展示过的奖励记录
func _load_shown_rewards() -> Dictionary:
    var settings = ConfigFile.new()
    var err = settings.load("user://settings.cfg")

    var shown_rewards = {}

    if err == OK:
        # 从 [rewards_shown] 节读取已展示的奖励
        var sections = settings.get_sections()
        if "rewards_shown" in sections:
            var keys = settings.get_section_keys("rewards_shown")
            for key in keys:
                if settings.get_value("rewards_shown", key, false):
                    shown_rewards[key] = true
            print("从 settings.cfg 加载了 %d 个已展示的奖励记录" % shown_rewards.size())
        else:
            print("settings.cfg 中没有找到 rewards_shown 节，所有奖励都未展示")
    else:
        print("无法加载 settings.cfg，所有奖励都未展示")

    return shown_rewards

# 标记奖励为已展示（供胜利页面调用）
func mark_reward_as_shown(reward_type: String):
    var settings = ConfigFile.new()
    var err = settings.load("user://settings.cfg")

    if err != OK:
        # 如果文件不存在，创建新的配置
        settings = ConfigFile.new()

    # 设置奖励为已展示（解锁）
    settings.set_value("rewards_shown", reward_type, true)

    # 检查是否已经有new_status记录，如果没有则标记为新卡牌（true表示新卡牌）
    var section_name = "rewards_new_status"
    var key = "rewards_" + reward_type
    if not settings.has_section_key(section_name, key):
        settings.set_value(section_name, key, true)  # 新解锁的奖励标记为"新"

    # 保存配置
    var save_err = settings.save("user://settings.cfg")
    if save_err == OK:
         # 上传steam
        SteamInfo.fileUpload("Settings",settings)
        print("标记奖励 %s 为已展示（解锁）" % reward_type)
    else:
        print("保存奖励展示状态失败: ", save_err)

# 从 settings.cfg 加载 buff_rewards 的启用状态
func _load_buff_rewards_enabled_states() -> Dictionary:
    var settings = ConfigFile.new()
    var err = settings.load("user://settings.cfg")

    var enabled_states = {}

    if err == OK:
        # 从 [buff_rewards_enabled] 节读取状态
        var sections = settings.get_sections()
        if "buff_rewards_enabled" in sections:
            var keys = settings.get_section_keys("buff_rewards_enabled")
            for key in keys:
                enabled_states[key] = settings.get_value("buff_rewards_enabled", key, false)
            print("从 settings.cfg 加载了 %d 个 buff_rewards 启用状态" % enabled_states.size())
        else:
            print("settings.cfg 中没有找到 buff_rewards_enabled 节，使用默认状态")
    else:
        print("无法加载 settings.cfg，使用默认 buff_rewards 状态")

    return enabled_states

# 合并卡牌数据
func _combine_card_data():
    all_card_data_list.clear()

    # 记录各分类的起始索引
    zoo_start_index = 0
    rewards_start_index = zoo_card_data_list.size()

    # 先添加 zoo 卡牌
    for card in zoo_card_data_list:
        all_card_data_list.append(card)

    # 再添加 rewards 卡牌
    for card in reward_card_data_list:
        all_card_data_list.append(card)

    print("合并后总共有 ", all_card_data_list.size(), " 张卡牌")
    print("Zoo卡牌起始索引: ", zoo_start_index, " Rewards卡牌起始索引: ", rewards_start_index)

# 加载测试数据（当BuffManager不可用时使用）
func _load_test_data():
    zoo_card_data_list.clear()
    reward_card_data_list.clear()
    all_card_data_list.clear()

    # 创建一些测试zoo卡牌数据
    var test_zoo_cards = [
        {
            "category": "zoo",
            "type": "tiger_buff",
            "name": "老虎牌",
            "display_name": "凶猛攻式",
            "description": "老虎: 3阶后+15分, 每阶扣2分",
            "texture_path": "res://assert/sign/tiger_buff.png",
            "is_unlocked": true
        },
        {
            "category": "zoo",
            "type": "rabbit_buff",
            "name": "兔子牌",
            "display_name": "乖乖还钱",
            "description": "兔子: 立刻+10分, 4阶扣分",
            "texture_path": "res://assert/sign/rabbit_buff.png",
            "is_unlocked": true
        },
        {
            "category": "zoo",
            "type": "frog_buff",
            "name": "青蛙牌",
            "display_name": "跳出格",
            "description": "青蛙: 分数上限70%, 禁用某花色",
            "texture_path": "res://assert/sign/frog_buff.png",
            "is_unlocked": false
        }
    ]

    # 创建一些测试rewards卡牌数据
    var test_reward_cards = [
        {
            "category": "rewards",
            "type": "dice_count",
            "name": "额外骰子",
            "display_name": "额外骰子",
            "description": "掷出骰子数量 +1",
            "texture_path": "res://assert/sign/dice_count_or_rolls.png",
            "is_unlocked": true
        },
        {
            "category": "rewards",
            "type": "hand_limit",
            "name": "手牌容量",
            "display_name": "手牌容量",
            "description": "保留手牌上限 +1",
            "texture_path": "res://assert/sign/hand_limit.png",
            "is_unlocked": false
        }
    ]

    # 添加到对应列表
    for card_data in test_zoo_cards:
        zoo_card_data_list.append(card_data)

    for card_data in test_reward_cards:
        reward_card_data_list.append(card_data)

    # 排序
    zoo_card_data_list.sort_custom(_sort_cards_by_unlock_status)
    reward_card_data_list.sort_custom(_sort_cards_by_unlock_status)

    # 合并数据
    _combine_card_data()

    print("加载了测试数据: zoo ", zoo_card_data_list.size(), " 张，rewards ", reward_card_data_list.size(), " 张")

# 卡牌排序函数：已解锁的排在前面
func _sort_cards_by_unlock_status(a, b):
    if a.is_unlocked and not b.is_unlocked:
        return true
    elif not a.is_unlocked and b.is_unlocked:
        return false
    else:
        # 如果解锁状态相同，按类型排序
        return a.type < b.type

# 获取本地化值
func _get_localized_value(value, locale: String = "") -> String:
    if locale == "":
        locale = TranslationServer.get_locale()

    if value is Dictionary:
        # 尝试获取当前语言的值
        if value.has(locale):
            return str(value[locale])
        # 回退到中文
        elif value.has("zh_CN"):
            return str(value["zh_CN"])
        # 回退到英语
        elif value.has("en"):
            return str(value["en"])
        # 如果都没有，返回第一个可用的值
        elif value.size() > 0:
            return str(value.values()[0])

    return str(value)

# 左滚动按钮点击
func _on_left_button_pressed():
    # 如果正在播放飞入动画，禁用滚动
    if is_playing_fly_in_animation:
        return

    # 播放按钮点击音效
    _play_scroll_button_click_sfx()

    # 播放按钮点击动画
    _play_scroll_button_animation(left_scroll_button)

    _scroll_cards(-1)  # 向左滚动一张卡牌

# 右滚动按钮点击
func _on_right_button_pressed():
    # 如果正在播放飞入动画，禁用滚动
    if is_playing_fly_in_animation:
        return

    # 播放按钮点击音效
    _play_scroll_button_click_sfx()

    # 播放按钮点击动画
    _play_scroll_button_animation(right_scroll_button)

    _scroll_cards(1)   # 向右滚动一张卡牌

# 播放滚动按钮点击音效
func _play_scroll_button_click_sfx():
    var audio_manager = get_node_or_null("/root/AudioManager")
    if audio_manager:
        audio_manager.play_sfx("res://assert/audio/sfx/button_click001.mp3")
    else:
        print("警告：无法获取AudioManager，无法播放按钮点击音效")

# 播放滚动按钮点击动画
func _play_scroll_button_animation(button: Button):
    if not button:
        return

    # 停止之前的动画（如果有）
    if button.has_meta("click_tween"):
        var existing_tween = button.get_meta("click_tween")
        if existing_tween and existing_tween.is_valid():
            existing_tween.kill()

    # 创建点击动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_BACK)  # 使用BACK过渡实现弹性效果

    # 按下效果：缩小
    tween.tween_property(button, "scale", Vector2(0.85, 0.85), 0.1)
    # 弹起效果：恢复并稍微超过原始大小，然后回到正常
    tween.tween_property(button, "scale", Vector2(1.05, 1.05), 0.15)
    tween.tween_property(button, "scale", Vector2(1.0, 1.0), 0.1)

    # 存储tween引用
    button.set_meta("click_tween", tween)

    # 清理tween引用
    tween.finished.connect(func(): button.remove_meta("click_tween"))

# 创建文字纹理（透明背景版本）
func _create_text_texture(text: String) -> ImageTexture:
    var text_width = 280  # 增加宽度以适应更大的字体
    var text_height = 70

    # 创建一个临时的Label来获取文字渲染信息
    var temp_label = Label.new()
    temp_label.text = text
    temp_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
    temp_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
    temp_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
    temp_label.size = Vector2(text_width, text_height)

    # 设置字体样式：金色文字，黑色边框，无阴影
    temp_label.add_theme_font_size_override("font_size", 24)  # 增大字体到24号
    temp_label.add_theme_color_override("font_color", Color(1.0, 0.8, 0.0, 1.0))  # 金色文字
    temp_label.add_theme_color_override("font_outline_color", Color(0.0, 0.0, 0.0, 1.0))  # 黑色边框
    temp_label.add_theme_constant_override("outline_size", 2)  # 增加边框大小以提高可读性
    temp_label.add_theme_color_override("font_shadow_color", Color(0.0, 0.0, 0.0, 0.0))  # 无阴影

    # 创建SubViewport并设置透明背景
    var viewport = SubViewport.new()
    viewport.size = Vector2i(text_width, text_height)
    viewport.render_target_update_mode = SubViewport.UPDATE_ONCE
    viewport.transparent_bg = true  # 设置透明背景
    viewport.add_child(temp_label)

    # 临时添加到场景树
    add_child(viewport)

    # 等待渲染完成
    await get_tree().process_frame
    await get_tree().process_frame

    # 获取渲染结果
    var texture = viewport.get_texture()
    var image_texture = ImageTexture.new()
    if texture:
        var image = texture.get_image()
        if image:
            image_texture.set_image(image)

    # 清理临时节点
    viewport.queue_free()

    return image_texture

# 异步创建并设置名称纹理
func _create_and_set_name_texture(text: String, shader_material: ShaderMaterial):
    var name_texture = await _create_text_texture(text)
    if name_texture and is_instance_valid(shader_material):
        shader_material.set_shader_parameter("card_name_texture", name_texture)
        shader_material.set_shader_parameter("show_name", true)

# 创建卡牌节点
func _create_card_nodes():
    # 清理现有节点
    for node in card_nodes:
        if is_instance_valid(node):
            node.queue_free()
    card_nodes.clear()
    
    # 为每张卡牌创建节点
    for i in range(all_card_data_list.size()):
        var card_data = all_card_data_list[i]
        var card_node = _create_single_card_node(card_data, i)
        card_container.add_child(card_node)
        card_nodes.append(card_node)

# 创建单个卡牌节点
func _create_single_card_node(card_data: Dictionary, index: int) -> Control:
    var card_node = Control.new()
    card_node.custom_minimum_size = base_card_size
    card_node.size = base_card_size

    # 创建使用shader的卡牌渲染节点
    var card_shader_rect = TextureRect.new()
    card_shader_rect.name = "CardShaderRect"
    card_shader_rect.anchors_preset = Control.PRESET_FULL_RECT
    card_shader_rect.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
    card_shader_rect.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
    card_shader_rect.size = base_card_size

    # 加载shader材质
    var shader_material = ShaderMaterial.new()
    var card_shader = load("res://shader/card_collection_effect.gdshader")
    shader_material.shader = card_shader

    # 设置基础纹理（用作主纹理）
    var main_texture = null
    if card_data.category == "zoo":
        # zoo卡牌使用统一背景
        main_texture = load("res://assert/cards/cardfront.svg")
    else:
        # buff_rewards卡牌使用texture_path作为背景
        if ResourceLoader.exists(card_data.texture_path):
            main_texture = load(card_data.texture_path)
        else:
            print("警告：buff_rewards卡牌纹理文件不存在：", card_data.texture_path)
            main_texture = load("res://assert/cards/cardfront.svg")

    if main_texture:
        card_shader_rect.texture = main_texture

    # 设置shader参数
    shader_material.set_shader_parameter("enable_float_effect", true)
    shader_material.set_shader_parameter("enable_3d_effect", false)  # 默认关闭，中心卡牌时开启
    shader_material.set_shader_parameter("enable_silhouette_effect", not card_data.is_unlocked)
    shader_material.set_shader_parameter("enable_shine_effect", card_data.is_unlocked)  # 已解锁卡牌启用闪光效果

    # 设置浮动效果参数（每张卡牌不同的时间偏移）
    shader_material.set_shader_parameter("float_amplitude", 5.0)
    shader_material.set_shader_parameter("float_frequency", 1.6)
    shader_material.set_shader_parameter("time_offset", index * 0.5)  # 错开浮动时间

    # 设置卡牌尺寸
    shader_material.set_shader_parameter("card_width", base_card_size.x)
    shader_material.set_shader_parameter("card_height", base_card_size.y)

    # 设置图标相关参数
    if card_data.category == "zoo" and card_data.is_unlocked:
        # zoo卡牌需要显示图标
        var icon_texture = null
        if ResourceLoader.exists(card_data.texture_path):
            icon_texture = load(card_data.texture_path)
        else:
            print("警告：卡牌纹理文件不存在：", card_data.texture_path)
            if ResourceLoader.exists("res://assert/sign/default.png"):
                icon_texture = load("res://assert/sign/default.png")

        if icon_texture:
            shader_material.set_shader_parameter("card_icon", icon_texture)
            shader_material.set_shader_parameter("show_icon", true)
        else:
            shader_material.set_shader_parameter("show_icon", false)
    else:
        shader_material.set_shader_parameter("show_icon", false)

    # 设置锁定图标
    if not card_data.is_unlocked:
        var lock_texture = load("res://assert/cards/lock.png")
        if lock_texture:
            shader_material.set_shader_parameter("lock_icon", lock_texture)
            shader_material.set_shader_parameter("show_lock", true)
        else:
            shader_material.set_shader_parameter("show_lock", false)
    else:
        shader_material.set_shader_parameter("show_lock", false)

    # 生成并设置卡牌名称纹理（异步）
    if card_data.is_unlocked:
        # 先设置为不显示，等纹理生成完成后再显示
        shader_material.set_shader_parameter("show_name", false)
        _create_and_set_name_texture(card_data.name, shader_material)
    else:
        shader_material.set_shader_parameter("show_name", false)

    # 应用shader材质
    card_shader_rect.material = shader_material
    card_node.add_child(card_shader_rect)

    # 添加"新"标记（如果需要）
    if card_data.get("is_new", false) and card_data.is_unlocked:
        var new_label = Label.new()
        new_label.name = "NewLabel"
        new_label.text = tr("NEW")  # 国际化文本
        new_label.anchors_preset = Control.PRESET_TOP_RIGHT
        new_label.anchor_left = 0.8
        new_label.anchor_top = 0.0
        new_label.anchor_right = 1.0
        new_label.anchor_bottom = 0.2
        new_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
        new_label.vertical_alignment = VERTICAL_ALIGNMENT_CENTER
        new_label.add_theme_font_size_override("font_size", 16)
        new_label.add_theme_color_override("font_color", Color(1.0, 0.8, 0.0, 1.0))  # 金色
        new_label.add_theme_color_override("font_shadow_color", Color(0.0, 0.0, 0.0, 0.8))
        new_label.add_theme_constant_override("shadow_offset_x", 1)
        new_label.add_theme_constant_override("shadow_offset_y", 1)
        card_node.add_child(new_label)

    # 存储卡牌数据
    card_node.set_meta("card_data", card_data)
    card_node.set_meta("card_index", index)

    return card_node

# 更新卡牌的shader效果
func _update_card_shader_effects():
    if card_nodes.is_empty():
        return

    for i in range(card_nodes.size()):
        var card_node = card_nodes[i]
        var shader_rect = card_node.get_child(0) as TextureRect  # CardShaderRect
        if shader_rect and shader_rect.material:
            var shader_material = shader_rect.material as ShaderMaterial
            # 确保3D效果关闭
            shader_material.set_shader_parameter("enable_3d_effect", false)


# 更新卡牌位置和缩放
func _update_card_positions():
    if card_nodes.is_empty():
        return

    # 如果正在播放飞入动画，跳过位置更新
    if is_playing_fly_in_animation:
        return

    # 使用CardDisplayArea的中心位置（不再需要考虑左右按钮）
    center_position = card_display_area.size.x / 2.0

    for i in range(card_nodes.size()):
        var card_node = card_nodes[i]
        if not is_instance_valid(card_node):
            continue

        # 计算卡牌的目标位置
        var target_x = center_position + (i * card_spacing) + scroll_offset
        # 使用CardDisplayArea的中心作为Y位置参考
        var target_y = card_display_area.size.y / 2.0 - base_card_size.y / 2.0

        # 计算距离中心的距离
        var distance_from_center = abs(target_x - center_position)

        # 计算缩放比例（使用平滑的缩放曲线）
        var scale_factor = max(min_scale, max_scale - (distance_from_center / scale_distance) * (max_scale - min_scale))
        scale_factor = max(min_scale, scale_factor)

        # 计算随机旋转角度（基于距离中心的位置）
        var rotation_angle = _calculate_card_rotation(i, distance_from_center)

        # 应用位置和缩放（从中心等比放大）
        # 计算缩放后的偏移量，使卡牌从中心向上下扩展
        var scaled_size = base_card_size * scale_factor
        var scale_offset_y = (scaled_size.y - base_card_size.y) / 2.0

        card_node.position = Vector2(target_x - scaled_size.x / 2.0, target_y - scale_offset_y)
        card_node.scale = Vector2(scale_factor, scale_factor)
        card_node.rotation = rotation_angle

        # 调整Z索引，中心的卡牌在最前面
        var card_z_index = int(100 - distance_from_center / 10)
        card_node.z_index = card_z_index

        # 调整透明度，距离中心越远越透明
        # var alpha = max(0.3, 1.0 - distance_from_center / (scale_distance * 2))
        # card_node.modulate.a = alpha

    # 检测中心卡牌变化并播放音效
    _check_center_card_change()

    # 更新当前中心卡牌的信息显示
    _update_center_card_info()

    # 更新卡牌shader效果
    _update_card_shader_effects()

# 检测中心卡牌变化并播放音效
func _check_center_card_change():
    # 如果正在播放飞入动画，不播放滚动音效
    if is_playing_fly_in_animation:
        return

    var current_center_index = _get_current_center_card_index()

    # 如果中心卡牌发生了变化，播放音效
    if current_center_index != last_center_card_index and last_center_card_index != -1:
        _play_card_scroll_sfx()

    # 更新记录的中心卡牌索引
    last_center_card_index = current_center_index

# 播放卡牌滚动音效
func _play_card_scroll_sfx():
    var current_time = Time.get_unix_time_from_system()

    # 检查冷却时间
    if current_time - last_scroll_sfx_time < scroll_sfx_cooldown:
        return

    var audio_manager = get_node_or_null("/root/AudioManager")
    if audio_manager:
        audio_manager.play_sfx("res://assert/audio/sfx/flip01.mp3")
        last_scroll_sfx_time = current_time
    else:
        print("警告：无法获取AudioManager，无法播放滚动音效")

# 计算卡牌的旋转角度
func _calculate_card_rotation(card_index: int, distance_from_center: float) -> float:
    # 如果卡牌在中心位置（距离很小），则不旋转
    var center_threshold = 50.0  # 中心区域的阈值
    if distance_from_center <= center_threshold:
        return 0.0

    # 使用卡牌索引作为随机种子，确保每张卡牌的旋转是固定的
    var rng = RandomNumberGenerator.new()
    rng.seed = hash(card_index)

    # 基础随机旋转角度（-15度到15度）
    var base_rotation = rng.randf_range(-15.0, 15.0)

    # 根据距离中心的远近调整旋转强度
    # 距离越远，旋转越明显
    var max_distance = scale_distance * 2.0  # 最大有效距离
    var distance_factor = min(distance_from_center / max_distance, 1.0)

    # 应用距离因子，让远离中心的卡牌旋转更明显
    var final_rotation = base_rotation * distance_factor

    # 调试输出（仅在前几张卡牌时输出，避免日志过多）
    # if card_index < 5:
    #     print("卡牌 %d: 距离=%.1f, 基础旋转=%.1f°, 距离因子=%.2f, 最终旋转=%.1f°" % [
    #         card_index, distance_from_center, base_rotation, distance_factor, final_rotation
    #     ])

    # 转换为弧度
    return deg_to_rad(final_rotation)

# 处理输入事件
func _input(event):
    # 如果正在播放飞入动画，禁用输入
    if is_playing_fly_in_animation:
        return

    if event is InputEventMouseButton:
        if event.button_index == MOUSE_BUTTON_WHEEL_UP:
            _scroll_cards(-1)
        elif event.button_index == MOUSE_BUTTON_WHEEL_DOWN:
            _scroll_cards(1)
        elif event.button_index == MOUSE_BUTTON_LEFT:
            if event.pressed:
                # 只处理拖拽，不再处理屏幕点击滚动
                _start_drag(event.position)
            else:
                _end_drag()
    elif event is InputEventMouseMotion:
        if is_dragging:
            _update_drag(event.position)
        # 总是更新shader效果以响应鼠标移动
        _update_card_shader_effects()

# 开始拖拽
func _start_drag(pos: Vector2):
    is_dragging = true
    drag_start_pos = pos
    drag_start_offset = scroll_offset

# 更新拖拽
func _update_drag(pos: Vector2):
    if not is_dragging:
        return

    var delta = pos.x - drag_start_pos.x
    # 增加拖拽敏感度，让鼠标移动一小段距离时卡牌滚动更多
    var drag_sensitivity = 2.0  # 拖拽敏感度倍数
    target_scroll_offset = drag_start_offset + delta * drag_sensitivity
    _clamp_scroll_offset()

# 结束拖拽
func _end_drag():
    is_dragging = false
    # 拖拽结束时，自动吸附到最近的卡牌中心
    _snap_to_nearest_card_immediately()
    last_interaction_time = Time.get_unix_time_from_system()

# 滚动卡牌
func _scroll_cards(direction: int):
    # 计算当前最接近中心的卡牌索引
    var current_center_index = _get_current_center_card_index()

    # 根据滚动方向移动到下一张或上一张卡牌
    var target_index = current_center_index + direction
    target_index = clamp(target_index, 0, card_nodes.size() - 1)

    # 直接设置目标偏移量到指定卡牌的中心位置
    target_scroll_offset = -target_index * card_spacing
    _clamp_scroll_offset()
    last_interaction_time = Time.get_unix_time_from_system()

# 限制滚动范围
func _clamp_scroll_offset():
    if card_nodes.is_empty():
        return

    var max_offset = card_spacing * 0.5  # 允许稍微超出第一张卡牌
    var min_offset = -(card_nodes.size() - 1) * card_spacing - card_spacing * 0.5  # 允许稍微超出最后一张卡牌
    target_scroll_offset = clamp(target_scroll_offset, min_offset, max_offset)

# 每帧更新
func _process(delta):
    # 如果正在播放飞入动画，跳过滚动更新
    if is_playing_fly_in_animation:
        return

    # 平滑滚动
    if abs(scroll_offset - target_scroll_offset) > 1.0:
        scroll_offset = lerp(scroll_offset, target_scroll_offset, delta * 8.0)
        _update_card_positions()
        # 根据滚动位置更新当前分类
        _update_current_category_by_scroll()
    # 滚动停止时不需要额外的吸附逻辑，因为滚动和拖拽都会直接定位到正确位置

    # 长按逻辑已移除，按钮仅支持单次点击

# 旧的屏幕点击处理函数已移除，现在使用按钮控制滚动

# 自动吸附到最近的卡牌
func _snap_to_nearest_card():
    if card_nodes.is_empty():
        return

    # 检查是否在交互冷却时间内（0.5秒）
    var current_time = Time.get_unix_time_from_system()
    if current_time - last_interaction_time < 0.1:
        return

    # 找到最接近中心的卡牌
    var closest_index = 0
    var min_distance = INF

    for i in range(card_nodes.size()):
        var card_node = card_nodes[i]
        if not is_instance_valid(card_node):
            continue

        # 计算卡牌中心到屏幕中心的距离
        var card_center_x = center_position + (i * card_spacing) + scroll_offset
        var distance = abs(card_center_x - center_position)

        if distance < min_distance:
            min_distance = distance
            closest_index = i

    # 计算目标滚动偏移量，使最近的卡牌居中
    var target_offset = -closest_index * card_spacing

    # 只有当偏移量差异足够大时才进行吸附
    if abs(target_scroll_offset - target_offset) > 10.0:
        target_scroll_offset = target_offset

# 动物卡牌标签页按钮点击事件
func _on_zoo_tab_pressed():
    # 如果正在播放飞入动画，禁用切换
    if is_playing_fly_in_animation:
        return

    if current_category == "zoo":
        return  # 已经在动物卡牌页面

    current_category = "zoo"
    _update_tab_buttons()
    _scroll_to_category_start()

# 奖励卡牌标签页按钮点击事件
func _on_rewards_tab_pressed():
    # 如果正在播放飞入动画，禁用切换
    if is_playing_fly_in_animation:
        return

    if current_category == "rewards":
        return  # 已经在奖励卡牌页面

    current_category = "rewards"
    _update_tab_buttons()
    _scroll_to_category_start()

# 更新标签页按钮状态
func _update_tab_buttons():
    if current_category == "zoo":
        zoo_tab_button.button_pressed = true
        rewards_tab_button.button_pressed = false
        # 更新按钮视觉效果
        _update_button_visual_state(zoo_tab_button, true)
        _update_button_visual_state(rewards_tab_button, false)
    else:
        zoo_tab_button.button_pressed = false
        rewards_tab_button.button_pressed = true
        # 更新按钮视觉效果
        _update_button_visual_state(zoo_tab_button, false)
        _update_button_visual_state(rewards_tab_button, true)

# 更新按钮的视觉状态
func _update_button_visual_state(button: Button, is_selected: bool):
    # 检查是否已初始化样式
    if not button.has_meta("style_initialized"):
        return

    # 使用按钮的modulate属性来控制颜色，避免重复创建StyleBox
    if is_selected:
        # 选中状态：发光效果
        button.modulate = Color(1.2, 1.2, 1.0, 1.0)
    else:
        # 未选中状态：阴影效果
        button.modulate = Color(0.7, 0.7, 0.7, 1.0)

# 设置标签页按钮的鼠标悬停效果
func _setup_tab_button_hover_effects():
    # 确保不重复连接信号
    if not zoo_tab_button.mouse_entered.is_connected(_on_tab_button_mouse_entered):
        zoo_tab_button.mouse_entered.connect(_on_tab_button_mouse_entered.bind(zoo_tab_button))
        zoo_tab_button.mouse_exited.connect(_on_tab_button_mouse_exited.bind(zoo_tab_button))

    if not rewards_tab_button.mouse_entered.is_connected(_on_tab_button_mouse_entered):
        rewards_tab_button.mouse_entered.connect(_on_tab_button_mouse_entered.bind(rewards_tab_button))
        rewards_tab_button.mouse_exited.connect(_on_tab_button_mouse_exited.bind(rewards_tab_button))

# 鼠标进入按钮时的Q弹放大效果
func _on_tab_button_mouse_entered(button: Button):
    # 停止之前的动画避免冲突
    if button.has_meta("hover_tween"):
        var existing_tween = button.get_meta("hover_tween")
        if existing_tween:
            existing_tween.kill()

    # 创建Q弹放大动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_BACK)  # 使用BACK过渡实现Q弹效果
    tween.tween_property(button, "scale", Vector2(1.1, 1.1), 0.2)

    # 存储tween引用
    button.set_meta("hover_tween", tween)

# 鼠标离开按钮时恢复正常大小
func _on_tab_button_mouse_exited(button: Button):
    # 停止之前的动画避免冲突
    if button.has_meta("hover_tween"):
        var existing_tween = button.get_meta("hover_tween")
        if existing_tween:
            existing_tween.kill()

    # 创建恢复动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_BACK)  # 使用BACK过渡实现Q弹效果
    tween.tween_property(button, "scale", Vector2(1.0, 1.0), 0.15)

    # 存储tween引用
    button.set_meta("hover_tween", tween)

# 滚动到当前分类的起始位置
func _scroll_to_category_start():
    var target_index = 0

    if current_category == "zoo":
        target_index = zoo_start_index
    elif current_category == "rewards":
        target_index = rewards_start_index

    # 计算目标滚动偏移量，使指定索引的卡牌居中
    target_scroll_offset = -target_index * card_spacing

    # 使用动画平滑滚动
    var tween = create_tween()
    tween.tween_property(self, "scroll_offset", target_scroll_offset, 0.5)
    tween.tween_callback(_update_card_positions)

# 更新当前分类显示（根据滚动位置自动切换标签页）
func _update_current_category_by_scroll():
    if all_card_data_list.is_empty():
        return

    # 计算当前中心位置对应的卡牌索引
    var center_card_index = int(round(-scroll_offset / card_spacing))
    center_card_index = clamp(center_card_index, 0, all_card_data_list.size() - 1)

    # 根据卡牌索引确定当前分类
    var new_category = ""
    if center_card_index < rewards_start_index:
        new_category = "zoo"
    else:
        new_category = "rewards"

    # 如果分类发生变化，更新标签页按钮
    if new_category != current_category:
        current_category = new_category
        _update_tab_buttons()

# 获取当前最接近中心的卡牌索引
func _get_current_center_card_index() -> int:
    if card_nodes.is_empty():
        return 0

    # 找到最接近中心的卡牌
    var closest_index = 0
    var min_distance = INF

    for i in range(card_nodes.size()):
        var card_node = card_nodes[i]
        if not is_instance_valid(card_node):
            continue

        # 计算卡牌中心到屏幕中心的距离
        var card_center_x = center_position + (i * card_spacing) + scroll_offset
        var distance = abs(card_center_x - center_position)

        if distance < min_distance:
            min_distance = distance
            closest_index = i

    return closest_index

# 立即吸附到最近的卡牌（用于拖拽结束）
func _snap_to_nearest_card_immediately():
    if card_nodes.is_empty():
        return

    var closest_index = _get_current_center_card_index()
    # 直接设置目标偏移量，使最近的卡牌居中
    target_scroll_offset = -closest_index * card_spacing
    _clamp_scroll_offset()

# 更新中心卡牌信息显示
func _update_center_card_info():
    var center_index = _get_current_center_card_index()
    if center_index >= 0 and center_index < all_card_data_list.size():
        var card_data = all_card_data_list[center_index]

        # 隐藏卡牌名称标签（名称现在显示在卡牌上）
        card_info_name_label.visible = false

        # 更新卡牌描述
        if card_data.is_unlocked:
            card_info_desc_label.text = card_data.description

            # 如果卡牌有"新"标记且已解锁，查看描述后隐藏"新"标记
            if card_data.get("is_new", false):
                _mark_card_as_viewed(card_data.category, card_data.type)
                _hide_new_label_for_card(center_index)
        else:
            card_info_desc_label.text = tr("LOCKED")
    else:
        # 没有卡牌时隐藏信息
        card_info_name_label.visible = false
        card_info_desc_label.text = tr("HAND_TYPE_UNKNOWN")

# 移除tooltip隐藏函数

# 返回按钮点击事件
func _on_return_button_pressed():
    # 恢复主菜单背景音乐
    _restore_main_menu_music()

    # 使用场景管理器切换回主菜单
    var scene_manager = get_node("/root/SceneManager")
    if scene_manager:
        scene_manager.change_scene_with_transition("res://scence/main_menu.tscn")

# 恢复主菜单背景音乐
func _restore_main_menu_music():
    var audio_manager = get_node_or_null("/root/AudioManager")
    if audio_manager:
        # 恢复主菜单背景音乐
        audio_manager.play_main_menu_music()
        print("返回主菜单：恢复主菜单背景音乐")
    else:
        print("警告：无法获取AudioManager，无法恢复背景音乐")

# 检查卡牌是否为新卡牌
func _is_card_new(category: String, card_type: String) -> bool:
    var settings = ConfigFile.new()
    var err = settings.load("user://settings.cfg")

    if err != OK:
        return false

    var section_name
    var key

    # 根据类别使用不同的section和key格式
    if category == "rewards":
        section_name = "rewards_new_status"
        key = "rewards_" + card_type
    else:
        section_name = "card_new_status"
        key = category + "_" + card_type

    # 如果没有记录，则不是新卡牌（可能是未解锁或旧数据）
    if not settings.has_section_key(section_name, key):
        return false

    # 返回存储的状态（true表示新卡牌，false表示已查看）
    return settings.get_value(section_name, key, false)

# 标记卡牌为已查看
func _mark_card_as_viewed(category: String, card_type: String):
    var settings = ConfigFile.new()
    settings.load("user://settings.cfg")  # 如果文件不存在会创建

    var section_name
    var key

    # 根据类别使用不同的section和key格式
    if category == "rewards":
        section_name = "rewards_new_status"
        key = "rewards_" + card_type
    else:
        section_name = "card_new_status"
        key = category + "_" + card_type

    # 设置为false表示已查看
    settings.set_value(section_name, key, false)
    # 保存配置
    var save_err = settings.save("user://settings.cfg")
    if save_err == OK:
         # 上传steam
        SteamInfo.fileUpload("Settings",settings)

    # 同时更新内存中的数据
    for i in range(all_card_data_list.size()):
        var card_data = all_card_data_list[i]
        if card_data.category == category and card_data.type == card_type:
            card_data.is_new = false
            break

# 隐藏指定卡牌的"新"标记
func _hide_new_label_for_card(card_index: int):
    if card_index < 0 or card_index >= card_nodes.size():
        return

    var card_node = card_nodes[card_index]
    if not is_instance_valid(card_node):
        return

    var new_label = card_node.get_node_or_null("CardContainer/NewLabel")
    if new_label:
        # 添加淡出动画
        var tween = create_tween()
        tween.tween_property(new_label, "modulate:a", 0.0, 0.3)
        tween.tween_callback(func(): new_label.queue_free())

# 新的排序函数：新解锁的在最前面，然后是已解锁的，最后是未解锁的
func _sort_cards_by_new_and_unlock_status(a: Dictionary, b: Dictionary) -> bool:
    # 首先按"新"状态排序
    if a.get("is_new", false) != b.get("is_new", false):
        return a.get("is_new", false)  # 新卡牌在前

    # 然后按解锁状态排序
    if a.is_unlocked != b.is_unlocked:
        return a.is_unlocked  # 已解锁在前

    # 最后按类型名称排序（保持一致性）
    return a.type < b.type

# 将描述中的所有占位符替换为灰色问号
func _replace_placeholders_with_question_marks(description: String) -> String:
    if description.is_empty():
        return description

    var result = description

    # 首先处理特殊情况：color= 前缀的占位符
    var color_placeholder_patterns = [
        "color=%d",           # color= + 整数占位符
        "color=%s",           # color= + 字符串占位符
        "color=%f",           # color= + 浮点数占位符
        "color=%.1f",         # color= + 一位小数占位符
        "color=%.2f",         # color= + 两位小数占位符
        "color=%.3f",         # color= + 三位小数占位符
        "color=%[0-9]+d",     # color= + 带宽度的整数占位符
        "color=%[0-9]+s",     # color= + 带宽度的字符串占位符
        "color=%[0-9]*\\.[0-9]+f"  # color= + 带精度的浮点数占位符
    ]

    # 替换 color= 前缀的占位符为 "color=purple"
    for pattern in color_placeholder_patterns:
        var regex = RegEx.new()
        regex.compile(pattern)
        result = regex.sub(result, "color=purple", true)

    # 然后处理其他普通占位符
    var placeholder_patterns = [
        "%d",           # 整数占位符
        "%s",           # 字符串占位符
        "%f",           # 浮点数占位符
        "%.1f",         # 一位小数占位符
        "%.2f",         # 两位小数占位符
        "%.3f",         # 三位小数占位符
        "%[0-9]+d",     # 带宽度的整数占位符，如 %2d
        "%[0-9]+s",     # 带宽度的字符串占位符，如 %10s
        "%[0-9]*\\.[0-9]+f"  # 带精度的浮点数占位符，如 %5.2f
    ]

    # 普通占位符替换为问号
    var gray_question_mark = "?"

    # 逐个替换占位符
    for pattern in placeholder_patterns:
        # 使用正则表达式替换
        var regex = RegEx.new()
        regex.compile(pattern)
        result = regex.sub(result, gray_question_mark, true)  # true表示替换所有匹配项

    return result

# 清理资源
func _exit_tree():
    # 现在使用shader显示名称，无需清理心跳效果
    pass
